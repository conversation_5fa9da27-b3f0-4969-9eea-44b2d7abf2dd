{"name": "receeto-project", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --configuration=optimized --open", "start:prod": "ng serve --configuration production --open", "start:quiet": "node serve.js", "start:super-quiet": "node serve-super-quiet.js", "start:clean": "node serve.js", "start:fix": "node fix-pkg-dir.js && ng serve --open", "fix-pkg-dir": "node fix-pkg-dir.js", "start:patched": "npm run fix-pkg-dir && ng serve --open", "start:optimized": "node start-optimized.js", "start:perfect": "node serve-perfect.js", "build": "ng build", "build:prod": "ng build --configuration production --stats-json", "analyze": "webpack-bundle-analyzer dist/receeto-project/stats.json", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "preinstall": "npx npm-force-resolutions"}, "private": true, "dependencies": {"@angular/animations": "^17.2.0", "@angular/cdk": "^17.2.0", "@angular/common": "^17.2.0", "@angular/compiler": "^17.2.0", "@angular/core": "^17.2.0", "@angular/forms": "^17.2.0", "@angular/material": "^17.2.0", "@angular/platform-browser": "^17.2.0", "@angular/platform-browser-dynamic": "^17.2.0", "@angular/router": "^17.2.0", "@fortawesome/fontawesome-free": "^6.7.2", "chart.js": "^4.4.9", "chartjs-plugin-datalabels": "^2.2.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "rxjs": "~7.8.0", "swiper": "^11.2.6", "tslib": "^2.5.0", "zone.js": "~0.14.0"}, "devDependencies": {"@angular-devkit/build-angular": "^17.2.0", "@angular-eslint/builder": "^17.2.0", "@angular-eslint/eslint-plugin": "^17.2.0", "@angular-eslint/eslint-plugin-template": "^17.2.0", "@angular-eslint/schematics": "^17.2.0", "@angular-eslint/template-parser": "^17.2.0", "@angular/cli": "^17.2.0", "@angular/compiler-cli": "^17.2.0", "@types/chart.js": "^2.9.41", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "ajv-formats": "^2.1.1", "autoprefixer": "^10.4.16", "chalk": "^4.1.2", "compression-webpack-plugin": "^10.0.0", "cross-env": "^7.0.3", "eslint": "^8.56.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "npm-force-resolutions": "0.0.10", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "terser-webpack-plugin": "^5.3.10", "typescript": "~5.2.2", "webpack-bundle-analyzer": "^4.10.1"}, "resolutions": {"mini-css-extract-plugin": "2.7.6"}}