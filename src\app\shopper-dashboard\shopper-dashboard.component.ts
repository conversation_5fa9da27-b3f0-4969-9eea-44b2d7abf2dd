import { Component, HostListener, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, On<PERSON><PERSON>roy, inject, computed, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { SidebarService } from '../sidebar/sidebar.service';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { ExpenseTrackerService } from '../services/expense-tracker.service';
import { TransactionService } from '../services/transaction.service';
import { DashboardDataService } from '../core/dashboard/dashboard-data.service';
import { AuthService } from '../core/auth/auth.service';
import { CardAuthDataService } from '../core/dashboard/card-auth-data.service';
import { ReceetAuthDataService } from '../core/receet/receet-auth-data.service';
import { FinancialSavingsDataService } from '../core/financial-savings/financial-savings-data.service';
import {
  LifetimeExpensesData,
  CurrentMonthData,
  ExpensesMonthData,
  SavingPlanData,
  MonthlyExpense,
  DailyExpense,
  SavingPlanItem
} from '../interfaces/dashboard';
import { Chart, DoughnutController, BarController, CategoryScale, LinearScale, ArcElement, BarElement, Tooltip } from 'chart.js';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';

// Register Chart.js components only once
Chart.register(DoughnutController, BarController, CategoryScale, LinearScale, ArcElement, BarElement, Tooltip);

// Chart performance defaults
Chart.defaults.animation = false; // Disable animations for better performance
Chart.defaults.responsive = true;
Chart.defaults.maintainAspectRatio = false;

interface Transaction {
  date: string;
  time: string;
  amount: string;
}

@Component({
  selector: 'app-shopper-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule, TopNavbarComponent, SidebarComponent],
  templateUrl: './shopper-dashboard.component.html',
  styleUrls: ['./shopper-dashboard.component.scss']
})
export class ShopperDashboardComponent implements AfterViewInit, OnDestroy {
  // Inject services using the new inject function
  private dashboardDataService = inject(DashboardDataService);
  private authService = inject(AuthService);
  private cardAuthDataService = inject(CardAuthDataService);
  private receetAuthDataService = inject(ReceetAuthDataService);
  private financialSavingsDataService = inject(FinancialSavingsDataService);

  // Signal-based reactive data
  dashboard = computed(() => this.dashboardDataService.dashboard());
  widgets = computed(() => this.dashboardDataService.widgets());
  analytics = computed(() => this.dashboardDataService.analytics());
  isLoading = computed(() => this.dashboardDataService.isLoading());
  currentUser = computed(() => this.authService.currentUser());
  isAuthenticated = computed(() => this.authService.isAuthenticated());

  // Computed user data from auth service
  user = computed(() => {
    const currentUser = this.currentUser();
    return currentUser ? {
      name: currentUser.full_name || 'User',
      role: currentUser.role || 'Shopper',
      avatar: currentUser.avatar || 'assets/images/default-avatar.svg'
    } : {
      name: 'Guest',
      role: 'Guest',
      avatar: 'assets/images/default-avatar.svg'
    };
  });

  // Computed stats from analytics and transactions
  stats = computed(() => {
    const analyticsData = this.analytics();
    const transactionCount = this.allTransactions.length;
    return {
      transactions: transactionCount,
      turnover: analyticsData.totalSpent || 0,
      mediumBasket: transactionCount > 0 ? (analyticsData.totalSpent / transactionCount) : 0,
      customerRetention: 92 // This would come from customer analytics
    };
  });

  // User-specific locations - these could be moved to a service later
  locations = computed(() => {
    const userEmail = this.currentUser()?.email;
    if (!userEmail) return [];

    // In a real app, this would come from a user-specific service
    // For now, we'll use different data based on user email
    const baseLocations = [
      { name: '001 - SOUSSE', tickets: 3520 },
      { name: '002 - TUNISIA MALL', tickets: 1620 },
      { name: '003 - SFAX MALL', tickets: 7862 }
    ];

    // Modify ticket counts based on user to show different data
    const userHash = userEmail.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
    return baseLocations.map((location, index) => ({
      ...location,
      tickets: location.tickets + (userHash % 1000) + (index * 100)
    }));
  });

  // Auth signals for the four new card components - delegated to CardAuthDataService

  // 1. Card Lifetime Expenses - Total sum of all transactions since account creation
  cardLifetimeExpenses = computed(() => this.cardAuthDataService.cardLifetimeExpenses());

  // 2. Card Current Month - Total sum of transactions in current calendar month
  cardCurrentMonth = computed(() => this.cardAuthDataService.cardCurrentMonth());

  // 3. Card Expenses Month - Monthly expense breakdown since account creation
  cardExpensesMonth = computed(() => this.cardAuthDataService.cardExpensesMonth());

  // 4. Card Saving Plan - Financial savings data integrated with chart-legend and savingPlanChart
  cardSavingPlan = computed(() => this.cardAuthDataService.cardSavingPlan());

  transactions: Transaction[] = [];
  allTransactions: any[] = []; // Store all transactions for the popup
  showTransactionsPopup: boolean = false; // Control popup visibility

  isMobile: boolean = window.innerWidth <= 768;

  private chart: Chart | null = null;
  private monthChart: Chart | null = null;
  private savingChart: Chart | null = null;
  private destroy$ = new Subject<void>();

  // Performance optimization flags
  private chartsInitialized = false;
  private chartUpdateQueue = new Map<string, any>();
  private isUpdatingCharts = false;
  private resizeObserver?: ResizeObserver;

  // Expose category expenses for the template
  categoryExpenses$: any;

  constructor(
    private sidebarService: SidebarService,
    private expenseTrackerService: ExpenseTrackerService,
    private transactionService: TransactionService
  ) {
    this.categoryExpenses$ = this.expenseTrackerService.categoryExpenses$;

    // Debug logging to track signal changes
    console.log('ShopperDashboardComponent constructor - Initial state:');
    console.log('- isAuthenticated:', this.isAuthenticated());
    console.log('- currentUser:', this.currentUser());
    console.log('- dashboard:', this.dashboard());
    console.log('- widgets:', this.widgets());

    // Effect to track when auth/dashboard signals change
    effect(() => {
      const user = this.currentUser();
      const isAuth = this.isAuthenticated();
      const dashboardData = this.dashboard();
      console.log('ShopperDashboard effect triggered:');
      console.log('- User changed:', user);
      console.log('- Is authenticated:', isAuth);
      console.log('- Dashboard data:', dashboardData);

      // Reload transactions and expenses when user changes
      if (isAuth && user) {
        console.log('Reloading transactions for user:', user.email);
        this.loadTransactions();

        // Force reload expenses for the new user
        console.log('Force reloading expenses for user:', user.email);
        this.expenseTrackerService.reloadExpenses();

        // Refresh card auth data service
        this.cardAuthDataService.refreshTransactions();

        // Update charts with new data
        setTimeout(() => {
          this.updateMonthlyChart();
          this.updateSavingChart();
        }, 100);
      } else {
        console.log('User not authenticated, clearing transactions and expenses');
        this.transactions = [];
        this.allTransactions = [];

        // Reset expenses to default
        this.expenseTrackerService.reloadExpenses();
      }
    });

    // Optimized effect to reactively update monthly chart with debouncing
    effect(() => {
      const monthlyData = this.cardExpensesMonth();
      if (monthlyData && this.chartsInitialized) {
        this.queueChartUpdate('monthly', monthlyData);
      }
    });

    // Optimized effect to reactively update saving chart with debouncing
    effect(() => {
      const savingData = this.cardSavingPlan();
      if (savingData && this.chartsInitialized) {
        this.queueChartUpdate('saving', savingData);
      }
    });

    // Effect to watch for ReceetAuthDataService transaction changes
    effect(() => {
      const userTransactions = this.receetAuthDataService.userTransactions();
      const currentUser = this.currentUser();

      console.log('ReceetAuthDataService transactions changed:', userTransactions.length);

      if (currentUser?.email && userTransactions) {
        // Update local transaction arrays when ReceetAuthDataService changes
        this.allTransactions = userTransactions;

        // Convert to dashboard format (last 10 transactions)
        this.transactions = userTransactions
          .slice(0, 10)
          .map((transaction: any) => ({
            date: transaction.date,
            time: this.extractTimeFromDate(transaction.date),
            amount: transaction.amount,
            ticketNumber: transaction.ticketNumber,
            productName: transaction.productName,
            paymentMode: transaction.paymentMode,
            status: transaction.status,
            brandName: transaction.brandName
          }));

        // Notify card auth service of changes
        this.cardAuthDataService.notifyTransactionAdded();

        console.log(`Dashboard updated with ${userTransactions.length} transactions from ReceetAuthDataService`);
      }
    });
  }

  ngAfterViewInit() {
    // Use requestAnimationFrame for better performance
    requestAnimationFrame(() => {
      this.initializeCharts();
      this.loadTransactions();
      this.chartsInitialized = true;

      // Subscribe to expense changes with debouncing
      this.expenseTrackerService.categoryExpenses$
        .pipe(
          takeUntil(this.destroy$),
          debounceTime(100),
          distinctUntilChanged()
        )
        .subscribe(() => {
          this.queueChartUpdate('category', null);
        });

      // Subscribe to new transactions with debouncing
      this.transactionService.currentTransaction
        .pipe(
          takeUntil(this.destroy$),
          debounceTime(200)
        )
        .subscribe((newTransaction) => {
          if (newTransaction) {
            this.loadTransactions();
          }
        });

      // Setup resize observer for responsive charts
      this.setupResizeObserver();
    });
  }

  private initializeCharts() {
    // Initialize charts with performance optimizations
    this.initializeCategoryChart();
    this.initializeMonthlyChart();
    this.initializeSavingChart();
  }

  // Optimized chart update queue system
  private queueChartUpdate(chartType: string, data: any) {
    this.chartUpdateQueue.set(chartType, data);

    if (!this.isUpdatingCharts) {
      this.isUpdatingCharts = true;
      requestAnimationFrame(() => {
        this.processChartUpdates();
        this.isUpdatingCharts = false;
      });
    }
  }

  private processChartUpdates() {
    for (const [chartType, data] of this.chartUpdateQueue) {
      switch (chartType) {
        case 'category':
          this.updateCategoryChart();
          break;
        case 'monthly':
          this.updateMonthlyChart();
          break;
        case 'saving':
          this.updateSavingChart();
          break;
      }
    }
    this.chartUpdateQueue.clear();
  }

  private initializeCategoryChart() {
    const ctxCategory = document.getElementById('expensesCategoryChart') as HTMLCanvasElement;
    if (ctxCategory) {
      if (this.chart) {
        this.chart.destroy();
        this.chart = null;
      }

      const chartData = this.expenseTrackerService.getChartData();

      this.chart = new Chart(ctxCategory, {
        type: 'doughnut',
        data: {
          labels: chartData.labels,
          datasets: [{
            data: chartData.data,
            backgroundColor: chartData.colors,
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          animation: false, // Disable animations for performance
          cutout: '70%',
          plugins: {
            legend: { display: false },
            tooltip: {
              enabled: true,
              callbacks: {
                label: function(context: any) {
                  return context.label + ': ' + context.parsed + '%';
                }
              }
            }
          }
        }
      });
    }
  }

  private updateCategoryChart() {
    if (this.chart) {
      const chartData = this.expenseTrackerService.getChartData();
      this.chart.data.labels = chartData.labels;
      this.chart.data.datasets[0].data = chartData.data;
      this.chart.data.datasets[0].backgroundColor = chartData.colors;
      this.chart.update('none'); // Use 'none' mode for instant updates
    }
  }

  private updateMonthlyChart() {
    console.log('🔍 updateMonthlyChart called, monthChart exists:', !!this.monthChart);
    if (this.monthChart) {
      const monthlyData = this.getMonthlyExpensesData();
      console.log('🔍 updateMonthlyChart - data:', monthlyData);

      // Show only first 7 expenses by default, but allow scrolling for more
      const displayData = monthlyData.length > 7 ? monthlyData.slice(0, 7) : monthlyData;
      const labels = displayData.length > 0
        ? displayData.map(month => month.monthName.split(' ')[0])
        : ['No Data'];
      const data = displayData.length > 0
        ? displayData.map(month => month.amount)
        : [0];

      console.log('🔍 updateMonthlyChart - labels:', labels);
      console.log('🔍 updateMonthlyChart - data:', data);

      // Calculate dynamic chart configuration based on data length
      const dataLength = displayData.length > 0 ? displayData.length : 1;
      const ctxMonth = document.getElementById('expensesMonthChart') as HTMLCanvasElement;
      const chartContainer = ctxMonth?.parentElement;

      // Apply dynamic width based on number of months
      if (ctxMonth && chartContainer) {
        let chartWidth: number;
        let responsive: boolean;

        if (dataLength === 1) {
          chartWidth = 200;
          responsive = false;
        } else if (dataLength === 2) {
          chartWidth = 320;
          responsive = false;
        } else if (dataLength === 3) {
          chartWidth = 450;
          responsive = false;
        } else {
          // For 4-7 months, use larger width to fill more space
          chartWidth = 650;
          responsive = false;
        }

        // Apply dynamic width to canvas and container
        if (!responsive) {
          // For all cases, use fixed sizing (no scrolling needed since we show max 7)
          // Add responsive constraint to prevent breaking layout
          const maxContainerWidth = Math.min(chartWidth, window.innerWidth * 0.8);
          chartContainer.style.maxWidth = `${maxContainerWidth}px`;
          chartContainer.style.margin = '0';
          chartContainer.style.marginLeft = '0';
          chartContainer.style.overflowX = 'visible';
          chartContainer.style.overflowY = 'visible';
          const aspectRatio = dataLength === 1 ? 1.2 : dataLength === 2 ? 1.5 : 1.8;
          chartContainer.style.width = `${maxContainerWidth}px`;
          chartContainer.style.height = `${Math.round(maxContainerWidth / aspectRatio)}px`;

          // Reset canvas styling
          ctxMonth.style.width = '100%';
          ctxMonth.style.height = '100%';
          ctxMonth.style.position = 'absolute';
        } else {
          // Reset all styles for responsive mode
          chartContainer.style.maxWidth = '';
          chartContainer.style.margin = '';
          chartContainer.style.marginLeft = '';
          chartContainer.style.width = '';
          chartContainer.style.height = '';
          chartContainer.style.overflowX = '';
          chartContainer.style.overflowY = '';
          ctxMonth.style.width = '';
          ctxMonth.style.height = '';
          ctxMonth.style.position = '';
        }

        // Add mouse wheel scroll functionality for navigating through months
        this.setupMouseWheelScroll(chartContainer, monthlyData);
      }

      this.monthChart.data.labels = labels;
      this.monthChart.data.datasets[0].data = data;
      this.monthChart.update('none'); // Use 'none' mode for instant updates
    }
  }

  private updateSavingChart() {
    if (this.savingChart) {
      const savingPlans = this.getSavingPlanItems();
      const labels = savingPlans.length > 0 ? savingPlans.map(plan => plan.name) : ['No Data'];
      const data = savingPlans.length > 0 ? savingPlans.map(plan => plan.percentage) : [100];
      const colors = savingPlans.length > 0 ? savingPlans.map(plan => plan.color) : ['#E5E7EB'];

      this.savingChart.data.labels = labels;
      this.savingChart.data.datasets[0].data = data;
      this.savingChart.data.datasets[0].backgroundColor = colors;
      this.savingChart.update('none'); // Use 'none' mode for instant updates
    }
  }

  private currentMonthOffset = 0; // Track current scroll position for months

  private initializeMonthlyChart() {
    const ctxMonth = document.getElementById('expensesMonthChart') as HTMLCanvasElement;
    console.log('🔍 initializeMonthlyChart - canvas element found:', !!ctxMonth);
    if (ctxMonth) {
      if (this.monthChart) {
        this.monthChart.destroy();
      }

      // Get monthly expenses data from auth signals
      const monthlyData = this.getMonthlyExpensesData();
      console.log('🔍 Monthly chart initialization - data:', monthlyData);

      // Show only first 7 expenses by default
      const displayData = monthlyData.length > 7 ? monthlyData.slice(0, 7) : monthlyData;
      const labels = displayData.length > 0
        ? displayData.map(month => month.monthName.split(' ')[0]) // Show only month name
        : ['No Data'];
      const data = displayData.length > 0
        ? displayData.map(month => month.amount)
        : [0];

      console.log('🔍 Chart labels:', labels);
      console.log('🔍 Chart data:', data);

      // Calculate dynamic chart configuration based on data length
      const dataLength = displayData.length > 0 ? displayData.length : 1;
      const chartContainer = ctxMonth.parentElement;

      // Set dynamic width based on number of months
      let chartWidth: number;
      let aspectRatio: number;
      let responsive: boolean;

      if (dataLength === 1) {
        chartWidth = 200; // Increased width for single month
        aspectRatio = 1.2;
        responsive = false;
      } else if (dataLength === 2) {
        chartWidth = 320; // Increased width for two months
        aspectRatio = 1.5;
        responsive = false;
      } else if (dataLength === 3) {
        chartWidth = 450; // Increased width for three months
        aspectRatio = 1.8;
        responsive = false;
      } else {
        // For 4-7 months, use larger width to fill more space
        chartWidth = 650;
        aspectRatio = 2.0;
        responsive = false;
      }

      // Apply dynamic width to canvas and container
      if (!responsive && chartContainer) {
        // For all cases, use fixed sizing (no scrolling needed since we show max 7)
        // Add responsive constraint to prevent breaking layout
        const maxContainerWidth = Math.min(chartWidth, window.innerWidth * 0.8);
        chartContainer.style.maxWidth = `${maxContainerWidth}px`;
        chartContainer.style.margin = '0';
        chartContainer.style.marginLeft = '0';
        chartContainer.style.overflowX = 'visible';
        chartContainer.style.overflowY = 'visible';
        chartContainer.style.width = `${maxContainerWidth}px`;
        chartContainer.style.height = `${Math.round(maxContainerWidth / aspectRatio)}px`;

        // Reset canvas styling
        ctxMonth.style.width = '100%';
        ctxMonth.style.height = '100%';
        ctxMonth.style.position = 'absolute';

        // Add mouse wheel scroll functionality for navigating through months
        this.setupMouseWheelScroll(chartContainer, monthlyData);
      } else if (chartContainer) {
        // Reset all styles for responsive behavior
        chartContainer.style.maxWidth = '';
        chartContainer.style.margin = '';
        chartContainer.style.marginLeft = '';
        chartContainer.style.width = '';
        chartContainer.style.height = '';
        chartContainer.style.overflowX = '';
        chartContainer.style.overflowY = '';
        ctxMonth.style.width = '';
        ctxMonth.style.height = '';
        ctxMonth.style.position = '';
      }

      this.monthChart = new Chart(ctxMonth, {
        type: 'bar',
        data: {
          labels: labels,
          datasets: [{
            label: 'Expenses',
            data: data,
            backgroundColor: '#A78BFA',
            borderWidth: 0
          }]
        },
        options: {
          responsive: responsive,
          maintainAspectRatio: !responsive,
          aspectRatio: aspectRatio,
          layout: {
            padding: {
              left: 0, // Ensure no extra padding on left
              right: 10,
              top: 10,
              bottom: 0
            }
          },
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                label: (context: any) => {
                  const amount = context.parsed.y % 1 === 0 ? context.parsed.y.toString() : context.parsed.y.toFixed(2);
                  return 'Expenses: ' + amount + ' TND';
                }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              position: 'left', // Ensure Y-axis is on the left
              grid: {
                display: true
              },
              ticks: {
                display: true // Ensure Y-axis labels are visible
              }
            },
            x: {
              grid: {
                display: false
              },
              ticks: {
                display: true // Ensure X-axis labels are visible
              }
            }
          }
        }
      });

      console.log('🔍 Monthly chart created successfully:', !!this.monthChart);
    } else {
      console.log('🔍 Canvas element not found for monthly chart');
    }
  }

  private initializeSavingChart() {
    const ctxSaving = document.getElementById('savingPlanChart') as HTMLCanvasElement;
    if (ctxSaving) {
      if (this.savingChart) {
        this.savingChart.destroy();
      }

      const chartOptions = {
        responsive: false,
        maintainAspectRatio: false,
        width: 280,
        height: 160
      };

      // Get saving plan data from auth signals
      const savingPlans = this.getSavingPlanItems();
      const labels = savingPlans.length > 0 ? savingPlans.map(plan => plan.name) : ['No Data'];
      const data = savingPlans.length > 0 ? savingPlans.map(plan => plan.percentage) : [100];
      const colors = savingPlans.length > 0 ? savingPlans.map(plan => plan.color) : ['#E5E7EB'];

      this.savingChart = new Chart(ctxSaving, {
        type: 'doughnut',
        data: {
          labels: labels,
          datasets: [{
            data: data,
            backgroundColor: colors,
            borderWidth: 2,
            borderColor: '#fff'
          }]
        },
        options: {
          ...chartOptions,
          cutout: '70%',
          rotation: -90,
          circumference: 180,
          plugins: {
            legend: { display: false },
            tooltip: {
              enabled: true,
              callbacks: {
                label: function(context: any) {
                  return context.label + ': ' + context.parsed.toFixed(1) + '%';
                }
              }
            }
          }
        }
      });
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.isMobile = window.innerWidth <= 768;
    // Debounce chart reinitialization on resize
    this.queueChartUpdate('resize', null);
  }

  // Setup resize observer for better performance
  private setupResizeObserver() {
    if (typeof ResizeObserver !== 'undefined') {
      this.resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (entry.target.id === 'expensesCategoryChart' ||
              entry.target.id === 'expensesMonthChart' ||
              entry.target.id === 'savingPlanChart') {
            this.queueChartUpdate('resize', null);
          }
        }
      });

      // Observe chart containers
      const categoryChart = document.getElementById('expensesCategoryChart');
      const monthChart = document.getElementById('expensesMonthChart');
      const savingChart = document.getElementById('savingPlanChart');

      if (categoryChart) this.resizeObserver.observe(categoryChart);
      if (monthChart) this.resizeObserver.observe(monthChart);
      if (savingChart) this.resizeObserver.observe(savingChart);
    }
  }

  toggleSidebar() {
    this.sidebarService.toggleSidebar();
  }

  /**
   * Load transactions using ReceetAuthDataService for consistency
   */
  private loadTransactions(): void {
    try {
      const currentUser = this.currentUser();
      if (!currentUser?.email) {
        console.log('No authenticated user, clearing transactions');
        this.transactions = [];
        this.allTransactions = [];
        return;
      }

      console.log(`Loading transactions for user: ${currentUser.email} using ReceetAuthDataService`);

      // Get transactions from the ReceetAuthDataService
      const userTransactions = this.receetAuthDataService.userTransactions();

      if (userTransactions && userTransactions.length > 0) {
        // Store all transactions for the popup
        this.allTransactions = userTransactions;

        // Convert to the format expected by the dashboard (last 10 transactions)
        this.transactions = userTransactions
          .slice(0, 10) // Get only the last 10 transactions
          .map((transaction: any) => ({
            date: transaction.date,
            time: this.extractTimeFromDate(transaction.date),
            amount: transaction.amount,
            // Add additional fields for popup display
            ticketNumber: transaction.ticketNumber,
            productName: transaction.productName,
            paymentMode: transaction.paymentMode,
            status: transaction.status,
            brandName: transaction.brandName
          }));

        console.log(`Loaded ${this.allTransactions.length} transactions for user: ${currentUser.email}`);

        // Notify card auth service that transactions have been updated
        this.cardAuthDataService.notifyTransactionAdded();
      } else {
        console.log(`No transactions found for user: ${currentUser.email}`);
        this.transactions = [];
        this.allTransactions = [];

        // Notify card auth service even when no transactions (to clear data)
        this.cardAuthDataService.notifyTransactionAdded();
      }
    } catch (error) {
      console.error('Error loading transactions from ReceetAuthDataService:', error);
      // Keep empty arrays if there's an error
      this.transactions = [];
      this.allTransactions = [];
    }
  }

  /**
   * Extract time from date or generate a default time
   */
  private extractTimeFromDate(dateStr: string): string {
    // If the date string contains time, extract it
    if (dateStr.includes(' ')) {
      const parts = dateStr.split(' ');
      return parts[1] || '12:00 PM';
    }

    // Generate a random time for display purposes
    const hours = Math.floor(Math.random() * 12) + 1;
    const minutes = Math.floor(Math.random() * 60);
    const ampm = Math.random() > 0.5 ? 'AM' : 'PM';
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')} ${ampm}`;
  }

  // Method to open the transactions popup
  openTransactionsPopup(): void {
    this.showTransactionsPopup = true;
    // Prevent body scrolling when popup is open
    document.body.style.overflow = 'hidden';
  }

  // Method to close the transactions popup
  closeTransactionsPopup(): void {
    this.showTransactionsPopup = false;
    // Restore body scrolling
    document.body.style.overflow = 'auto';
  }

  // Method to handle clicking outside the popup to close it
  onPopupBackdropClick(event: MouseEvent): void {
    if (event.target === event.currentTarget) {
      this.closeTransactionsPopup();
    }
  }



  // Method to calculate total amount of all transactions
  calculateTotalAmount(): string {
    if (!this.allTransactions || this.allTransactions.length === 0) {
      return '0';
    }

    const total = this.allTransactions.reduce((sum, transaction) => {
      // Extract numeric value from amount string (e.g., "123.45 TND" -> 123.45)
      const amountStr = transaction.amount?.toString() || '0';
      const numericAmount = parseFloat(amountStr.replace(/[^\d.-]/g, '')) || 0;
      return sum + numericAmount;
    }, 0);

    return this.formatAmount(total);
  }

  // Method to get CSS class for transaction status
  getStatusClass(status: string): string {
    if (!status) return 'status-completed';

    switch (status.toLowerCase()) {
      case 'completed':
        return 'status-completed';
      case 'pending':
        return 'status-pending';
      case 'cancelled':
      case 'canceled':
        return 'status-cancelled';
      case 'failed':
        return 'status-failed';
      default:
        return 'status-completed';
    }
  }

  // Helper methods for template calculations
  calculateTimeSpent(): string {
    const userEmail = this.currentUser()?.email;
    if (!userEmail) return '0Y 0D 0H';

    // Calculate based on user registration or first transaction
    // For demo purposes, use a calculation based on user email
    const userHash = userEmail.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
    const years = Math.floor(userHash % 5) + 1;
    const days = Math.floor(userHash % 365);
    const hours = Math.floor(userHash % 24);

    return `${years}Y ${days}D ${hours}H`;
  }

  calculateMonthlyExpenses(): string {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    // Filter transactions for current month
    const monthlyTransactions = this.allTransactions.filter(transaction => {
      const transactionDate = new Date(transaction.date);
      return transactionDate.getMonth() === currentMonth &&
             transactionDate.getFullYear() === currentYear;
    });

    const total = monthlyTransactions.reduce((sum, transaction) => {
      const amountStr = transaction.amount?.toString() || '0';
      const numericAmount = parseFloat(amountStr.replace(/[^\d.-]/g, '')) || 0;
      return sum + numericAmount;
    }, 0);

    return this.formatAmount(total);
  }

  calculateMonthlyArticles(): number {
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    return this.allTransactions.filter(transaction => {
      const transactionDate = new Date(transaction.date);
      return transactionDate.getMonth() === currentMonth &&
             transactionDate.getFullYear() === currentYear;
    }).length;
  }

  calculateMonthlyTime(): string {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const diffTime = Math.abs(now.getTime() - startOfMonth.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor((diffTime % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    return `${diffDays}D ${diffHours}H`;
  }

  // Helper methods for accessing auth signal data in templates - delegated to CardAuthDataService

  // Get lifetime expenses total amount
  getLifetimeExpensesTotal(): string {
    return this.formatAmount(this.cardAuthDataService.getLifetimeExpensesTotal());
  }

  // Get lifetime expenses transaction count
  getLifetimeExpensesCount(): number {
    const data = this.cardLifetimeExpenses();
    return data ? data.transactionCount : 0;
  }

  // Get current month expenses total
  getCurrentMonthTotal(): string {
    return this.formatAmount(this.cardAuthDataService.getCurrentMonthTotal());
  }

  // Get current month transaction count
  getCurrentMonthCount(): number {
    const data = this.cardCurrentMonth();
    return data ? data.transactionCount : 0;
  }

  // Get monthly expenses breakdown for chart
  getMonthlyExpensesData(): MonthlyExpense[] {
    const data = this.cardAuthDataService.getMonthlyExpensesData();
    console.log('🔍 getMonthlyExpensesData called, returning:', data);
    return data;
  }

  // Get average monthly expenses
  getAverageMonthlyExpenses(): string {
    const data = this.cardExpensesMonth();
    return data ? this.formatAmount(data.averageMonthlyAmount) : '0';
  }

  // Get saving plan data for chart-legend integration
  getSavingPlanItems(): SavingPlanItem[] {
    return this.cardAuthDataService.getSavingPlanItems();
  }

  // Get total savings progress percentage
  getTotalSavingsPercentage(): number {
    return this.cardAuthDataService.getTotalSavingsPercentage();
  }

  // Get total savings current amount
  getTotalSavingsCurrent(): string {
    const data = this.cardSavingPlan();
    return data ? this.formatAmount(data.totalCurrent) : '0';
  }

  // Get total savings target amount
  getTotalSavingsTarget(): string {
    const data = this.cardSavingPlan();
    return data ? this.formatAmount(data.totalTarget) : '0';
  }

  // Helper method for month names
  getMonthName(monthIndex: number): string {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[monthIndex] || 'Unknown';
  }

  // Helper method to format numbers - only show decimals when needed
  formatAmount(amount: number): string {
    if (amount % 1 === 0) {
      // If the number is a whole number, don't show decimals
      return amount.toString();
    } else {
      // If the number has decimals, show up to 2 decimal places
      return amount.toFixed(2);
    }
  }

  // Add just 2-3 fake saving plans for testing
  addFakeSavingPlans(): void {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot add fake data: No authenticated user');
      return;
    }

    const fakePlans = [
      {
        name: 'Vacation Fund',
        objective: 2000,
        current: 1200,
        period: 'OneYear' as any,
        frequency: 'Monthly' as any,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        interestRate: 2.5
      },
      {
        name: 'Emergency Fund',
        objective: 5000,
        current: 1800,
        period: 'OneYear' as any,
        frequency: 'Monthly' as any,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2025-01-01'),
        interestRate: 3.0
      },
      {
        name: 'New Car',
        objective: 15000,
        current: 4500,
        period: 'Custom' as any,
        frequency: 'Monthly' as any,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2026-01-01'),
        interestRate: 1.8
      }
    ];

    // Add each fake plan
    fakePlans.forEach(planData => {
      this.financialSavingsDataService.addSavingPlan(planData).subscribe(newPlan => {
        if (newPlan) {
          console.log(`✅ Added fake saving plan: ${newPlan.name}`);
        }
      });
    });

    console.log(`🎯 Added ${fakePlans.length} fake saving plans for testing`);
  }

  // Clear all saving plans for testing
  clearAllSavingPlans(): void {
    this.financialSavingsDataService.clearAllSavingPlans();
    console.log('🗑️ Cleared all saving plans - dashboard should show empty state');
  }

  // Add fake monthly data for chart width testing - 12 months
  addFakeMonthlyData(): void {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot add fake data: No authenticated user');
      return;
    }

    // Generate fake transactions for 12 months (Jan 2024 - Dec 2024)
    const fakeTransactions = [
      // January 2024
      { ticketNumber: 1001, productName: 'Groceries', productId: '#1001', date: '2024-01-15', amount: `${this.formatAmount(85.5)} TND`, paymentMode: 'Card', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.5, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
      { ticketNumber: 1002, productName: 'Coffee', productId: '#1002', date: '2024-01-20', amount: `${this.formatAmount(12)} TND`, paymentMode: 'Cash', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.0, brandName: 'Starbucks', brandLogo: 'assets/images/brands/starbucks.png' },

      // February 2024
      { ticketNumber: 1003, productName: 'Books', productId: '#1003', date: '2024-02-10', amount: `${this.formatAmount(45.75)} TND`, paymentMode: 'Card', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.8, brandName: 'Bookstore', brandLogo: 'assets/images/brands/bookstore.png' },
      { ticketNumber: 1004, productName: 'Lunch', productId: '#1004', date: '2024-02-25', amount: `${this.formatAmount(30)} TND`, paymentMode: 'Cash', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.2, brandName: 'Restaurant', brandLogo: 'assets/images/brands/restaurant.png' },

      // March 2024
      { ticketNumber: 1005, productName: 'Clothes', productId: '#1005', date: '2024-03-05', amount: `${this.formatAmount(120)} TND`, paymentMode: 'Card', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.6, brandName: 'Fashion Store', brandLogo: 'assets/images/brands/fashion.png' },
      { ticketNumber: 1006, productName: 'Gas', productId: '#1006', date: '2024-03-18', amount: `${this.formatAmount(65.4)} TND`, paymentMode: 'Card', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.0, brandName: 'Gas Station', brandLogo: 'assets/images/brands/gas.png' },

      // April 2024
      { ticketNumber: 1007, productName: 'Electronics', productId: '#1007', date: '2024-04-08', amount: `${this.formatAmount(89.25)} TND`, paymentMode: 'Card', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.7, brandName: 'Tech Store', brandLogo: 'assets/images/brands/tech.png' },
      { ticketNumber: 1008, productName: 'Pharmacy', productId: '#1008', date: '2024-04-22', amount: `${this.formatAmount(25)} TND`, paymentMode: 'Cash', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.3, brandName: 'Pharmacy', brandLogo: 'assets/images/brands/pharmacy.png' },

      // May 2024
      { ticketNumber: 1009, productName: 'Groceries', productId: '#1009', date: '2024-05-12', amount: `${this.formatAmount(95)} TND`, paymentMode: 'Card', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.4, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },
      { ticketNumber: 1010, productName: 'Restaurant', productId: '#1010', date: '2024-05-28', amount: `${this.formatAmount(42.5)} TND`, paymentMode: 'Card', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.1, brandName: 'Restaurant', brandLogo: 'assets/images/brands/restaurant.png' },

      // June 2024
      { ticketNumber: 1011, productName: 'Sports', productId: '#1011', date: '2024-06-05', amount: `${this.formatAmount(78)} TND`, paymentMode: 'Card', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.5, brandName: 'Sports Store', brandLogo: 'assets/images/brands/sports.png' },
      { ticketNumber: 1012, productName: 'Beauty', productId: '#1012', date: '2024-06-19', amount: `${this.formatAmount(33.75)} TND`, paymentMode: 'Cash', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.2, brandName: 'Beauty Store', brandLogo: 'assets/images/brands/beauty.png' },

      // July 2024
      { ticketNumber: 1013, productName: 'Home & Garden', productId: '#1013', date: '2024-07-10', amount: `${this.formatAmount(156)} TND`, paymentMode: 'Card', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.6, brandName: 'Home Store', brandLogo: 'assets/images/brands/home.png' },
      { ticketNumber: 1014, productName: 'Coffee', productId: '#1014', date: '2024-07-25', amount: `${this.formatAmount(15.5)} TND`, paymentMode: 'Cash', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.0, brandName: 'Starbucks', brandLogo: 'assets/images/brands/starbucks.png' },

      // August 2024
      { ticketNumber: 1015, productName: 'Electronics', productId: '#1015', date: '2024-08-08', amount: `${this.formatAmount(210)} TND`, paymentMode: 'Card', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.8, brandName: 'Tech Store', brandLogo: 'assets/images/brands/tech.png' },
      { ticketNumber: 1016, productName: 'Groceries', productId: '#1016', date: '2024-08-20', amount: `${this.formatAmount(67.25)} TND`, paymentMode: 'Card', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.3, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },

      // September 2024
      { ticketNumber: 1017, productName: 'Books', productId: '#1017', date: '2024-09-12', amount: `${this.formatAmount(38)} TND`, paymentMode: 'Card', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.7, brandName: 'Bookstore', brandLogo: 'assets/images/brands/bookstore.png' },
      { ticketNumber: 1018, productName: 'Fashion', productId: '#1018', date: '2024-09-28', amount: `${this.formatAmount(89.9)} TND`, paymentMode: 'Cash', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.4, brandName: 'Fashion Store', brandLogo: 'assets/images/brands/fashion.png' },

      // October 2024
      { ticketNumber: 1019, productName: 'Gas', productId: '#1019', date: '2024-10-15', amount: `${this.formatAmount(72)} TND`, paymentMode: 'Card', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.1, brandName: 'Gas Station', brandLogo: 'assets/images/brands/gas.png' },
      { ticketNumber: 1020, productName: 'Restaurant', productId: '#1020', date: '2024-10-30', amount: `${this.formatAmount(54.5)} TND`, paymentMode: 'Card', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.5, brandName: 'Restaurant', brandLogo: 'assets/images/brands/restaurant.png' },

      // November 2024
      { ticketNumber: 1021, productName: 'Pharmacy', productId: '#1021', date: '2024-11-08', amount: `${this.formatAmount(28)} TND`, paymentMode: 'Cash', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.2, brandName: 'Pharmacy', brandLogo: 'assets/images/brands/pharmacy.png' },
      { ticketNumber: 1022, productName: 'Groceries', productId: '#1022', date: '2024-11-22', amount: `${this.formatAmount(103.75)} TND`, paymentMode: 'Card', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.6, brandName: 'Carrefour', brandLogo: 'assets/images/brands/carrefour.png' },

      // December 2024
      { ticketNumber: 1023, productName: 'Gifts', productId: '#1023', date: '2024-12-10', amount: `${this.formatAmount(145)} TND`, paymentMode: 'Card', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.8, brandName: 'Gift Store', brandLogo: 'assets/images/brands/gifts.png' },
      { ticketNumber: 1024, productName: 'Electronics', productId: '#1024', date: '2024-12-25', amount: `${this.formatAmount(267.5)} TND`, paymentMode: 'Card', status: 'Completed', productImage: 'https://placehold.co/24x24', rating: 4.9, brandName: 'Tech Store', brandLogo: 'assets/images/brands/tech.png' }
    ];

    // Add fake transactions through ReceetAuthDataService
    fakeTransactions.forEach(transaction => {
      const receetTransaction = {
        ...transaction,
        userId: currentUser.email
      };
      this.receetAuthDataService.addTransaction(receetTransaction);
    });

    // The effects will automatically update the dashboard when transactions change
    console.log('Fake transactions added through ReceetAuthDataService');

    console.log(`✅ Added ${fakeTransactions.length} fake transactions across 12 months for chart width testing`);

    // Force chart update after a short delay to ensure data is processed
    setTimeout(() => {
      console.log('🔍 Forcing chart update after adding fake data');
      this.updateMonthlyChart();
    }, 200);
  }

  // Setup mouse wheel and swipe scroll functionality for navigating through months
  private setupMouseWheelScroll(chartContainer: HTMLElement, monthlyData: any[]): void {
    if (monthlyData.length <= 7) {
      // No need for scrolling if 7 or fewer months
      return;
    }

    // Remove existing event listeners if any
    chartContainer.removeEventListener('wheel', this.handleMouseWheel);
    chartContainer.removeEventListener('touchstart', this.handleTouchStart);
    chartContainer.removeEventListener('touchmove', this.handleTouchMove);
    chartContainer.removeEventListener('touchend', this.handleTouchEnd);

    // Mouse wheel event listener
    this.handleMouseWheel = (event: WheelEvent) => {
      event.preventDefault();
      const scrollDirection = event.deltaY > 0 ? 1 : -1;
      this.updateChartScroll(scrollDirection, monthlyData);
    };

    // Touch event listeners for swipe functionality
    let startX = 0;
    let startY = 0;
    let isScrolling = false;

    this.handleTouchStart = (event: TouchEvent) => {
      startX = event.touches[0].clientX;
      startY = event.touches[0].clientY;
      isScrolling = false;
    };

    this.handleTouchMove = (event: TouchEvent) => {
      if (!startX || !startY) return;

      const currentX = event.touches[0].clientX;
      const currentY = event.touches[0].clientY;
      const diffX = startX - currentX;
      const diffY = startY - currentY;

      // Determine if this is a horizontal swipe
      if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 30) {
        event.preventDefault();
        isScrolling = true;
      }
    };

    this.handleTouchEnd = (event: TouchEvent) => {
      if (!startX || !startY || !isScrolling) return;

      const endX = event.changedTouches[0].clientX;
      const diffX = startX - endX;

      // Minimum swipe distance
      if (Math.abs(diffX) > 50) {
        const scrollDirection = diffX > 0 ? 1 : -1; // Swipe left = next, swipe right = previous
        this.updateChartScroll(scrollDirection, monthlyData);
      }

      // Reset values
      startX = 0;
      startY = 0;
      isScrolling = false;
    };

    // Add event listeners
    chartContainer.addEventListener('wheel', this.handleMouseWheel, { passive: false });
    chartContainer.addEventListener('touchstart', this.handleTouchStart, { passive: false });
    chartContainer.addEventListener('touchmove', this.handleTouchMove, { passive: false });
    chartContainer.addEventListener('touchend', this.handleTouchEnd, { passive: false });
  }

  private updateChartScroll(scrollDirection: number, monthlyData: any[]): void {
    const maxOffset = Math.max(0, monthlyData.length - 7);
    this.currentMonthOffset = Math.max(0, Math.min(maxOffset, this.currentMonthOffset + scrollDirection));

    // Update chart with new data slice
    const displayData = monthlyData.slice(this.currentMonthOffset, this.currentMonthOffset + 7);
    const labels = displayData.map(month => month.monthName.split(' ')[0]);
    const data = displayData.map(month => month.amount);

    if (this.monthChart) {
      this.monthChart.data.labels = labels;
      this.monthChart.data.datasets[0].data = data;
      this.monthChart.update();
    }
  }

  private handleMouseWheel!: (event: WheelEvent) => void;
  private handleTouchStart!: (event: TouchEvent) => void;
  private handleTouchMove!: (event: TouchEvent) => void;
  private handleTouchEnd!: (event: TouchEvent) => void;

  // Clear monthly data for testing
  clearMonthlyData(): void {
    const currentUser = this.authService.currentUser();
    if (!currentUser) {
      console.error('Cannot clear data: No authenticated user');
      return;
    }

    // Clear all transactions through ReceetAuthDataService
    this.receetAuthDataService.setTransactions([]);

    // Also clear expenses category data
    this.expenseTrackerService.resetExpenses();

    // Reset month offset for scrolling
    this.currentMonthOffset = 0;

    // The effects will automatically update the dashboard when transactions change
    console.log('🗑️ Cleared all monthly data and expenses category data - charts should show minimal width');
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();

    // Restore body scrolling if component is destroyed while popup is open
    document.body.style.overflow = 'auto';

    // Clean up chart instances
    if (this.chart) {
      this.chart.destroy();
      this.chart = null;
    }
    if (this.monthChart) {
      this.monthChart.destroy();
      this.monthChart = null;
    }
    if (this.savingChart) {
      this.savingChart.destroy();
      this.savingChart = null;
    }

    // Clean up resize observer
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = undefined;
    }

    // Clear update queue
    this.chartUpdateQueue.clear();

    // Clean up event listeners
    const chartContainer = document.getElementById('expensesMonthChart')?.parentElement;
    if (chartContainer) {
      if (this.handleMouseWheel) {
        chartContainer.removeEventListener('wheel', this.handleMouseWheel);
      }
      if (this.handleTouchStart) {
        chartContainer.removeEventListener('touchstart', this.handleTouchStart);
      }
      if (this.handleTouchMove) {
        chartContainer.removeEventListener('touchmove', this.handleTouchMove);
      }
      if (this.handleTouchEnd) {
        chartContainer.removeEventListener('touchend', this.handleTouchEnd);
      }
    }
  }
}