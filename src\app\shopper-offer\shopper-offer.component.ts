import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit, OnDestroy, ViewChild, ElementRef, AfterViewInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { SwiperOptions } from 'swiper/types';
import { ThemeService } from '../services/theme.service';
import { NotificationService } from '../notification-system/notification.service';
import { TransactionService } from '../services/transaction.service';
import { ExpenseTrackerService } from '../services/expense-tracker.service';
import { AuthService } from '../core/auth/auth.service';
import { ReceetAuthDataService } from '../core/receet/receet-auth-data.service';
import { ReceetTransaction } from '../interfaces/receet';
import { Subject } from 'rxjs';
import { takeUntil, take } from 'rxjs/operators';
import { QuantityModalComponent } from '../quantity-modal/quantity-modal.component';

interface Offer {
  id: number;
  images: string[]; // Array of images for the slider
  profile: {
    name: string;
    avatar: string;
    date: string;
  };
  tags: string[];
  isFavorite: boolean;
  price: number; // Price of the offer
  productName?: string; // Optional product name
}

@Component({
  selector: 'app-shopper-offer',
  templateUrl: './shopper-offer.component.html',
  styleUrls: ['./shopper-offer.component.scss'],
  standalone: true,
  imports: [CommonModule, TopNavbarComponent, SidebarComponent, QuantityModalComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ShopperOfferComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('offersContainer') offersContainer!: ElementRef;

  // Inject services
  private authService = inject(AuthService);
  private receetAuthDataService = inject(ReceetAuthDataService);

  isDarkMode = false;
  private destroy$ = new Subject<void>();
  showQuantityModal = false;
  selectedOffer: Offer | null = null;
  offers: Offer[] = [
    {
      id: 1,
      images: [
        'assets/images/Recetto Avo.png',
        'assets/images/Bana 2.jpg',
        'assets/images/Bana 3.jpg'
      ],
      profile: {
        name: 'Aziza',
        avatar: 'assets/images/Aziza logo.png',
        date: '08 Jun 2025'
      },
      tags: ['food'],
      isFavorite: false,
      price: 16.99,
      productName: 'Avocado Toast'
    },
    {
      id: 2,
      images: [
        'assets/images/Recetto burg.png',
        'assets/images/humb 3.jpg',
        'assets/images/carrefour 3.webp'
      ],
      profile: {
        name: 'Carrefour',
        avatar: 'assets/images/Carrefour logo.png',
        date: '08 Jun 2025'
      },
      tags: ['food'],
      isFavorite: false,
      price: 24.50,
      productName: 'Gourmet Burger'
    },
    {
      id: 3,
      images: [
        'assets/images/Clothes 1.jpg',
        'assets/images/Clothes 2.jpg',
        'assets/images/Clothes 3.jpg'
      ],
      profile: {
        name: 'Zen',
        avatar: 'assets/images/Zen logo.png',
        date: '08 Jun 2025'
      },
      tags: ['clothes'],
      isFavorite: false,
      price: 53.00,
      productName: 'T-Shirt'
    },
    {
      id: 4,
      images: [
        'assets/images/Medicines 1.jpg',
        'assets/images/Medicines 2.jpg',
        'assets/images/Medicines 3.jpg'
      ],
      profile: {
        name: 'Monoprix',
        avatar: 'assets/images/brands/monoprix.png',
        date: '08 Jun 2025'
      },
      tags: ['healthy', 'medicines'],
      isFavorite: false,
      price: 9.00,
      productName: 'Panadol'
    },
    {
      id: 5,
      images: [
        'assets/images/Leisure 1.jpg',
        'assets/images/Leisure 2.jpg',
        'assets/images/Leisure 3.jpg'
      ],
      profile: {
        name: 'Strass Sahloul',
        avatar: 'assets/images/brands/Strass Sahloul logo.png',
        date: '08 Jun 2025'
      },
      tags: ['leisure'],
      isFavorite: false,
      price: 12.00,
      productName: 'Ludo'
    },
    {
      id: 6,
      images: [
        'assets/images/chess 1.jpg',
        'assets/images/chess 2.jpg',
        'assets/images/chess 3.jpg'
      ],
      profile: {
        name: 'Strass Sahloul',
        avatar: 'assets/images/brands/Strass Sahloul logo.png',
        date: '08 Jun 2025'
      },
      tags: ['leisure'],
      isFavorite: false,
      price: 90.00,
      productName: 'Chess'
    }
  ];

  getSwiperConfig(offerId: number): SwiperOptions {
    return {
      slidesPerView: 1,
      spaceBetween: 10,
      pagination: {
        clickable: true,
        el: `.swiper-pagination-${offerId}`
      },
      navigation: false // Disable navigation buttons
    };
  }

  toggleFavorite(offer: Offer): void {
    offer.isFavorite = !offer.isFavorite;
  }

  /**
   * Handle image loading errors by replacing with default image
   */
  handleImageError(event: Event): void {
    const imgElement = event.target as HTMLImageElement;

    // Check if this is a profile avatar or an offer image
    if (imgElement.classList.contains('profile-avatar')) {
      // Use default brand logo for profile avatars
      imgElement.src = 'assets/images/default-brand-logo.png';
    } else {
      // For offer images, use a different default image
      imgElement.src = 'assets/images/default-brand-logo.png';
    }

    // Prevent further error events
    imgElement.onerror = null;
  }

  constructor(
    private themeService: ThemeService,
    private notificationService: NotificationService,
    private transactionService: TransactionService,
    private expenseTrackerService: ExpenseTrackerService
  ) {}

  ngOnInit(): void {
    // Subscribe to theme changes with improved performance
    this.themeService.isDarkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isDark => {
        // Use requestAnimationFrame to ensure UI updates happen during the next paint cycle
        requestAnimationFrame(() => {
          this.isDarkMode = isDark;
        });
      });

    // Add window resize listener
    window.addEventListener('resize', this.boundCheckForOverflow);
  }

  ngAfterViewInit(): void {
    // Check for overflow after view is initialized
    setTimeout(() => {
      this.checkForOverflow();
    }, 100);
  }

  /**
   * Check if the content overflows and add/remove the has-overflow class and scrollable class accordingly
   */
  private checkForOverflow(): void {
    if (this.offersContainer && this.offersContainer.nativeElement) {
      const container = this.offersContainer.nativeElement;

      // Reset the container height to auto to get accurate measurements
      container.style.height = 'auto';

      // Get the content height and viewport height
      const contentHeight = container.scrollHeight;
      const viewportHeight = window.innerHeight - 64; // Subtract header height

      // Check if content height is greater than viewport height
      const hasOverflow = contentHeight > viewportHeight;

      // Apply appropriate classes
      if (hasOverflow) {
        container.classList.add('has-overflow');
        container.classList.add('scrollable');
        // Set fixed height to enable scrolling
        container.style.height = `${viewportHeight}px`;
      } else {
        container.classList.remove('has-overflow');
        container.classList.remove('scrollable');
        // Keep auto height when no overflow
        container.style.height = 'auto';
      }

      console.log(`Content height: ${contentHeight}, Viewport height: ${viewportHeight}, Has overflow: ${hasOverflow}`);
    }
  }

  /**
   * Open the quantity modal
   * @param offer The offer to purchase
   * @param event The click event
   */
  openQuantityModal(offer: Offer, event: Event): void {
    // Prevent event propagation to avoid triggering other click handlers
    event.stopPropagation();

    // Set the selected offer and show the modal
    this.selectedOffer = offer;
    this.showQuantityModal = true;
  }

  /**
   * Close the quantity modal
   */
  closeQuantityModal(): void {
    this.showQuantityModal = false;
    this.selectedOffer = null;
  }

  /**
   * Handle the quantity confirmation
   * @param data The quantity and total price data
   */
  onQuantityConfirm(data: { quantity: number, total: number }): void {
    if (!this.selectedOffer) return;

    // Create a transaction record with the selected quantity
    const transaction = {
      ticketNumber: Math.floor(10000 + Math.random() * 90000), // Generate a random ticket number
      productId: `#${this.selectedOffer.id}${Math.floor(1000 + Math.random() * 9000)}`,
      productName: this.selectedOffer.productName || 'Product',
      productImage: this.selectedOffer.images[0],
      date: new Date().toLocaleDateString('en-GB'), // Format: DD/MM/YYYY
      amount: `${this.formatAmount(data.total)} TND`,
      paymentMode: 'Online Payment',
      status: 'Completed',
      quantity: data.quantity,
      rating: 0,
      brandName: this.selectedOffer.profile.name,
      brandLogo: this.selectedOffer.profile.avatar,
      products: Array(data.quantity).fill({
        name: this.selectedOffer.productName || 'Product',
        price: this.selectedOffer.price
      })
    };

    // Save the transaction to localStorage for persistence
    this.saveTransaction(transaction);

    // Track the expense by category based on tags
    this.expenseTrackerService.addExpenseFromTags(this.selectedOffer.tags, data.total);

    // Show notification in the app
    this.showAppNotification(this.selectedOffer, data.quantity, data.total);

    // Show browser notification
    this.showBrowserNotification(this.selectedOffer, data.quantity, data.total);

    // Close the modal
    this.closeQuantityModal();
  }

  /**
   * Save the transaction using ReceetAuthDataService
   * @param transaction The transaction to save
   */
  private saveTransaction(transaction: any): void {
    try {
      // Get current authenticated user
      const currentUser = this.authService.currentUser();
      if (!currentUser?.email) {
        console.error('Cannot save transaction: No authenticated user');
        return;
      }

      // Convert to ReceetTransaction format
      const receetTransaction: ReceetTransaction = {
        ticketNumber: transaction.ticketNumber,
        productName: transaction.productName,
        productId: transaction.productId,
        date: transaction.date,
        amount: transaction.amount,
        paymentMode: transaction.paymentMode,
        status: transaction.status,
        productImage: transaction.productImage,
        rating: transaction.rating,
        brandName: transaction.brandName,
        brandLogo: transaction.brandLogo,
        products: transaction.products,
        quantity: transaction.quantity,
        userId: currentUser.email
      };

      // Add transaction through the auth service
      this.receetAuthDataService.addTransaction(receetTransaction);

      console.log(`Transaction saved for user: ${currentUser.email} through ReceetAuthDataService`);

      // Update the transaction service for backward compatibility
      this.transactionService.setCurrentTransaction(transaction);
    } catch (e) {
      console.error('Error saving transaction through ReceetAuthDataService:', e);
    }
  }

  /**
   * Show a notification in the app
   * @param offer The offer that was purchased
   * @param quantity The quantity purchased
   * @param total The total price
   */
  private showAppNotification(offer: Offer, quantity: number = 1, total?: number): void {
    const finalTotal = total !== undefined ? total : offer.price * quantity;

    // Add a purchase notification
    this.notificationService.addPurchaseNotification(
      offer.profile.name,
      offer.productName || 'Product',
      finalTotal,
      quantity
    );

    // The notification service will automatically show the panel
  }

  /**
   * Show a browser notification
   * @param offer The offer that was purchased
   * @param quantity The quantity purchased
   * @param total The total price
   */
  private showBrowserNotification(offer: Offer, quantity: number = 1, total?: number): void {
    // First check if Marketing Notifications are enabled
    this.notificationService.getNotificationSettings().pipe(
      take(1) // Take only the first emission to avoid memory leaks
    ).subscribe(settings => {
      // Only proceed if Marketing Notifications are enabled
      if (settings.marketingNotifications.enabled) {
        // Check if browser notifications are supported
        if ('Notification' in window) {
          // Check if permission is already granted
          if (Notification.permission === 'granted') {
            this.createBrowserNotification(offer, quantity, total);
          }
          // Check if permission is not denied (first time request)
          else if (Notification.permission !== 'denied') {
            // Request permission
            Notification.requestPermission().then(permission => {
              if (permission === 'granted') {
                this.createBrowserNotification(offer, quantity, total);
              }
            });
          }
        }
      } else {
        console.log('Browser notifications are disabled in user settings');
      }
    });
  }

  /**
   * Create and show a browser notification
   * @param offer The offer that was purchased
   * @param quantity The quantity purchased
   * @param total The total price
   */
  private createBrowserNotification(offer: Offer, quantity: number = 1, total?: number): void {
    const finalTotal = total !== undefined ? total : offer.price * quantity;
    const title = 'Purchase Successful!';
    const quantityText = quantity > 1 ? `${quantity} x ` : '';
    const options = {
      body: `You have successfully purchased ${quantityText}${offer.productName || 'a product'} from ${offer.profile.name} for ${this.formatAmount(finalTotal)} TND.`,
      icon: offer.profile.avatar,
      image: offer.images[0],
      badge: 'assets/images/default-brand-logo.png'
    };

    // Create and show the notification
    const notification = new Notification(title, options);

    // Close the notification after 5 seconds
    setTimeout(() => {
      notification.close();
    }, 5000);

    // Handle notification click
    notification.onclick = () => {
      // Focus on the window when notification is clicked
      window.focus();
      notification.close();
    };
  }

  // Store the bound function reference to properly remove it later
  private boundCheckForOverflow = this.checkForOverflow.bind(this);

  // Helper method to format numbers - only show decimals when needed
  formatAmount(amount: number): string {
    if (amount % 1 === 0) {
      // If the number is a whole number, don't show decimals
      return amount.toString();
    } else {
      // If the number has decimals, show up to 2 decimal places
      return amount.toFixed(2);
    }
  }

  ngOnDestroy(): void {
    // Remove the resize event listener
    window.removeEventListener('resize', this.boundCheckForOverflow);

    // Clean up subscriptions
    this.destroy$.next();
    this.destroy$.complete();
  }
}