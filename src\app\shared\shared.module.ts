import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProgressCircleComponent } from './progress-circle/progress-circle.component';
import { TrendChartComponent } from './trend-chart/trend-chart.component';
import { ExpenseListComponent } from './expense-list/expense-list.component';

@NgModule({
  imports: [
    CommonModule,
    ProgressCircleComponent,
    TrendChartComponent,
    ExpenseListComponent
  ],
  exports: [
    ProgressCircleComponent,
    TrendChartComponent,
    ExpenseListComponent
  ]
})
export class SharedModule { }