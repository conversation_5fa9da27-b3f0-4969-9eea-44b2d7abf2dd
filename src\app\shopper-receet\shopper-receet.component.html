<app-top-navbar></app-top-navbar>
<app-sidebar></app-sidebar>

<div class="receet-container">
  <div class="receet-header">
    <h1>Transaction History</h1>
    <button class="add-ticket-btn" (click)="openAddTicketModal()">
      <i class="fas fa-plus"></i>
      Add Ticket
    </button>
  </div>

  <div class="receet-content">
    <div class="table-controls">
      <div class="controls-wrapper">
        <div class="entries-selector">
          <label>Show</label>
          <select #entriesSelect="ngModel" [(ngModel)]="entriesPerPage" (change)="updateEntries()" name="entriesPerPage">
            <option [ngValue]="10">10</option>
            <option [ngValue]="25">25</option>
            <option [ngValue]="50">50</option>
            <option [ngValue]="100">100</option>
          </select>

        </div>
        <div class="search-bar">
          <button class="delete-btn"
                  [disabled]="selectedTickets.size === 0"
                  (click)="deleteSelectedTickets()">
            <i class="fas fa-trash"></i>
            Delete ({{ selectedTickets.size }})
          </button>
          <div class="select-all-container">
            <button class="select-all-btn"
                    [disabled]="filteredTransactions.length === 0"
                    (click)="toggleSelectAllDropdown($event)">
              <i class="fas fa-ellipsis-v"></i>
              Select All Actions ({{ filteredTransactions.length }})
            </button>
            <div class="select-all-dropdown modal-style" *ngIf="showSelectAllDropdown">
              <div class="modal-header">
                <h3>Select Action</h3>
              </div>
              <div class="modal-body">
                <button (click)="handleBulkAction('delete')" class="action-btn">
                  <i class="fas fa-trash"></i>
                  <span>Delete All</span>
                </button>
                <button (click)="handleBulkAction('export')" class="action-btn">
                  <i class="fas fa-file-export"></i>
                  <span>Export All</span>
                </button>
                <button (click)="handleBulkAction('budget')" class="action-btn">
                  <i class="fas fa-wallet"></i>
                  <span>Add All to Budget</span>
                </button>
              </div>
            </div>
          </div>
          <button class="export-btn"
                  [disabled]="selectedTickets.size === 0"
                  (click)="exportSelectedTickets()">
            <i class="fas fa-file-export"></i>
            Export ({{ selectedTickets.size }})
          </button>
          <button class="budget-btn"
                  [disabled]="selectedTickets.size === 0"
                  (click)="addToBudget()">
            <i class="fas fa-wallet"></i>
            Add to Budget ({{ selectedTickets.size }})
          </button>
          <input type="text"
                 [(ngModel)]="searchQuery"
                 (input)="filterTransactions()"
                 placeholder="Search by product name or brand name"
                 class="search-input" />
        </div>
      </div>
    </div>

    <div class="table-container">
      <div class="table-responsive">
        <table class="transaction-table">
          <thead>
            <tr>
              <th>Select</th>
              <th>Brand name & Products </th>
              <th class="mobile-hide">ID</th>
              <th class="tablet-hide">Date</th>
              <th>Amount</th>
              <th class="tablet-hide">Payment Method</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let transaction of displayedTransactions; trackBy: trackByFn"
                (click)="viewTicketDetails(transaction, $event)"
                class="transaction-row"
                [class.selected]="selectedTickets.has(transaction.ticketNumber)"
                [attr.data-ticket-number]="transaction.ticketNumber"
                [attr.data-product-id]="transaction.productId">
              <td class="select-column">
                <i class="fas"
                   [class.fa-square]="!selectedTickets.has(transaction.ticketNumber)"
                   [class.fa-check-square]="selectedTickets.has(transaction.ticketNumber)"
                   (click)="toggleTicketSelection(transaction.ticketNumber, $event)"
                ></i>
              </td>
              <td>
                <div class="product-cell">
                  <div class="brand-logo-container">
                    <ng-container *ngIf="isCollaboratingBrand(transaction.brandName); else unknownBrandLogo">
                      <img [src]="transaction.brandName === 'STRASS SOUSSE MALL' ? 'assets/images/brands/STRASS SOUSSE MALL.png' :
                                  transaction.brandName === 'Strass Sahloul' ? 'assets/images/brands/Strass Sahloul logo.png' :
                                  transaction.brandLogo"
                           [alt]="transaction.brandName"
                           class="brand-logo" />
                    </ng-container>
                    <ng-template #unknownBrandLogo>
                      <div class="brand-logo unknown-brand">
                        <i class="fas fa-store text-gray-400"></i>
                      </div>
                    </ng-template>
                  </div>
                  <div class="product-details">
                    <div class="brand-info">
                      <span class="brand-name">{{ transaction.brandName }}</span>
                      <span class="product-name">{{ transaction.productName }}</span>
                      <span class="product-quantity" *ngIf="getProductQuantity(transaction) > 1">
                        ({{ getProductQuantity(transaction) }} items)
                      </span>
                    </div>
                    <div class="product-rating">
                      <i class="fas fa-star"></i>
                      <i class="fas fa-star" *ngIf="transaction.rating >= 2"></i>
                      <i class="fas fa-star" *ngIf="transaction.rating >= 3"></i>
                      <i class="fas fa-star" *ngIf="transaction.rating >= 4"></i>
                      <i class="fas fa-star-half-alt" *ngIf="transaction.rating % 1 >= 0.5"></i>
                      <i class="far fa-star" *ngIf="transaction.rating % 1 < 0.5 && transaction.rating < 5"></i>
                      <span class="rating-value">{{ transaction.rating }}/5</span>
                    </div>
                  </div>
                </div>
              </td>
              <td class="mobile-hide">{{ transaction.productId }}</td>
              <td class="tablet-hide">{{ transaction.date }}</td>
              <td>{{ transaction.amount }}</td>
              <td class="tablet-hide">
                <div class="payment-method">
                  <span class="payment-label">{{ transaction.paymentMode }}</span>
                  <!-- Credit Card Icons -->
                  <span class="payment-icon" *ngIf="transaction.paymentMode === 'Credit Card'"><i class="fas fa-credit-card"></i></span>
                  <span class="payment-icon" *ngIf="transaction.paymentMode === 'Visa'"><i class="fab fa-cc-visa"></i></span>
                  <span class="payment-icon" *ngIf="transaction.paymentMode === 'MasterCard'"><i class="fab fa-cc-mastercard"></i></span>
                  <span class="payment-icon" *ngIf="transaction.paymentMode === 'American Express'"><i class="fab fa-cc-amex"></i></span>
                  <span class="payment-icon" *ngIf="transaction.paymentMode === 'Discover'"><i class="fab fa-cc-discover"></i></span>

                  <!-- Cash and Bank Transfer Icons -->
                  <span class="payment-icon" *ngIf="transaction.paymentMode === 'Cash'"><i class="fas fa-money-bill-wave"></i></span>
                  <span class="payment-icon" *ngIf="transaction.paymentMode === 'Transfer Bank' || transaction.paymentMode === 'Bank Transfer'"><i class="fas fa-university"></i></span>

                  <!-- Digital Payment Icons -->
                  <span class="payment-icon" *ngIf="transaction.paymentMode === 'PayPal'"><i class="fab fa-paypal"></i></span>
                  <span class="payment-icon" *ngIf="transaction.paymentMode === 'Apple Pay'"><i class="fab fa-apple-pay"></i></span>
                  <span class="payment-icon" *ngIf="transaction.paymentMode === 'Google Pay'"><i class="fab fa-google-pay"></i></span>

                  <!-- Delivery Payment Icons -->
                  <span class="payment-icon" *ngIf="transaction.paymentMode === 'Cash on Delivery' || transaction.paymentMode.includes('Delivery')"><i class="fas fa-truck"></i></span>

                  <!-- Default Icon for other payment methods -->
                  <span class="payment-icon" *ngIf="!['Credit Card', 'Visa', 'MasterCard', 'American Express', 'Discover', 'Cash', 'Transfer Bank', 'Bank Transfer', 'PayPal', 'Apple Pay', 'Google Pay', 'Cash on Delivery'].includes(transaction.paymentMode)"><i class="fas fa-money-check-alt"></i></span>
                </div>
              </td>
              <td>
                <span [class]="getStatusClass(transaction.status)">{{ transaction.status }}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="pagination">
        <button class="pagination-btn" (click)="previousPage()" [disabled]="currentPage === 1">
          <i class="fas fa-chevron-left"></i>
        </button>
        <div class="page-numbers tablet-hide">
          <button *ngFor="let page of getPageNumbers()"
                  [class.active]="page === currentPage"
                  (click)="goToPage(page)">
            {{ page }}
          </button>
        </div>
        <button class="pagination-btn" (click)="nextPage()" [disabled]="currentPage === totalPages()">
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>
  </div>
</div>

<app-add-ticket-modal
  *ngIf="showAddTicketModal"
  (close)="showAddTicketModal = false"
  (save)="handleTicketSave($event)">
</app-add-ticket-modal>

<app-budget-selection-modal
  *ngIf="showBudgetModal"
  [totalAmount]="selectedTicketsAmount"
  (close)="showBudgetModal = false"
  (selectBudget)="handleBudgetSelection($event)">
</app-budget-selection-modal>

