import { Component, AfterViewInit, Input, ElementRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Chart, registerables } from 'chart.js';

Chart.register(...registerables);

@Component({
  selector: 'app-trend-chart',
  standalone: true,
  imports: [CommonModule],
  template: '<canvas #canvas></canvas>',
  styles: [`
    :host {
      display: block;
      width: 100%;
      height: 100%;
    }
  `]
})
export class TrendChartComponent implements AfterViewInit {
  @ViewChild('canvas') canvas!: ElementRef; // Added ! to indicate definite assignment
  @Input() data: number[] = [];
  private chart?: Chart;

  ngAfterViewInit() {
    if (this.canvas) {
      this.createChart();
    }
  }

  private createChart() {
    const ctx = this.canvas.nativeElement.getContext('2d');
    
    this.chart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: Array(this.data.length).fill(''),
        datasets: [{
          data: this.data,
          borderColor: '#6B48FF',
          borderWidth: 2,
          tension: 0.4,
          fill: false,
          pointRadius: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          x: {
            display: false
          },
          y: {
            display: false
          }
        }
      }
    });
  }
}