<app-top-navbar></app-top-navbar>
<app-sidebar></app-sidebar>

<div class="offers-container" #offersContainer>
  <div class="offers-header">
    <h1>Premium Offers</h1>
  </div>

  <div class="offers-grid">
    <div class="offer-wrapper" *ngFor="let offer of offers">
      <div class="offer-card">
        <!-- Product Badge -->
        <div class="product-badge">
          <span>{{ offer.productName }}</span>
        </div>

        <!-- Image Slider -->
        <div class="image-slider-container">
          <swiper-container [config]="getSwiperConfig(offer.id)">
            <swiper-slide *ngFor="let image of offer.images">
              <div class="offer-image">
                <img [src]="image" [alt]="offer.profile.name + ' offer'" (error)="handleImageError($event)">
              </div>
            </swiper-slide>
          </swiper-container>
          <div class="swiper-pagination swiper-pagination-{{offer.id}}"></div>

          <!-- Favorite Button (Overlay) -->
          <button class="favorite-btn-overlay" (click)="toggleFavorite(offer)">
            <i class="fas" [class.fa-heart]="offer.isFavorite" [class.fa-heart-o]="!offer.isFavorite"></i>
          </button>
        </div>

        <!-- Brand Section -->
        <div class="brand-section">
          <div class="profile">
            <div class="avatar-container">
              <img [src]="offer.profile.avatar" [alt]="offer.profile.name" class="profile-avatar" (error)="handleImageError($event)">
            </div>
            <div class="profile-info">
              <h3>{{ offer.profile.name }}</h3>
              <span class="date">{{ offer.profile.date }}</span>
            </div>
          </div>
        </div>

        <!-- Tags Section -->
        <div class="tags">
          <span class="tag" *ngFor="let tag of offer.tags">
            #{{ tag }}
          </span>
        </div>

        <!-- Price and Buy Section -->
        <div class="price-buy-section">
          <div class="price-container">
            <div class="price-label">Price</div>
            <div class="price-value">{{ formatAmount(offer.price) }} <span class="currency">TND</span></div>
          </div>

          <button class="buy-now-btn" (click)="openQuantityModal(offer, $event)">
            <i class="fas fa-shopping-cart"></i>
            <span>Buy Now</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Quantity Modal -->
<app-quantity-modal
  *ngIf="showQuantityModal && selectedOffer"
  [productName]="selectedOffer.productName || 'Product'"
  [productPrice]="selectedOffer.price"
  [brandName]="selectedOffer.profile.name"
  [productImage]="selectedOffer.images && selectedOffer.images.length > 0 ? selectedOffer.images[0] : ''"
  (confirm)="onQuantityConfirm($event)"
  (cancel)="closeQuantityModal()">
</app-quantity-modal>