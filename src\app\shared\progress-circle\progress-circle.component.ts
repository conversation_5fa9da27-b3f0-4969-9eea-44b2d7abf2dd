import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-progress-circle',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="progress-circle" [class]="type">
      <svg viewBox="0 0 36 36">
        <path class="circle-bg"
          d="M18 2.0845
            a 15.9155 15.9155 0 0 1 0 31.831
            a 15.9155 15.9155 0 0 1 0 -31.831"
        />
        <path class="circle"
          [style.strokeDasharray]="percentage + ', 100'"
          d="M18 2.0845
            a 15.9155 15.9155 0 0 1 0 31.831
            a 15.9155 15.9155 0 0 1 0 -31.831"
        />
      </svg>
      <div class="percentage">{{ percentage }}%</div>
      <div class="amount" *ngIf="showAmount && currentAmount !== undefined && totalAmount !== undefined">
        {{ formatAmount(currentAmount) }} / {{ formatAmount(totalAmount) }} {{ currency }}
      </div>
      <div class="label">{{ label }}</div>
    </div>
  `,
  styles: [`
    .progress-circle {
      position: relative;
      width: 100%;
      max-width: 200px;
      margin: 0 auto;
    }

    svg {
      width: 100%;
      height: 100%;
      transform: rotate(-90deg);
    }

    .circle-bg {
      fill: none;
      stroke: #edf2f7;
      stroke-width: 2.8;
    }

    .circle {
      fill: none;
      stroke-width: 2.8;
      stroke-linecap: round;
      transition: stroke-dasharray 0.3s ease;
    }

    .savings .circle {
      stroke: #6b48ff; 
    }

    .budget .circle {
      stroke: #48bb78; 
    }

    .percentage {
      position: absolute;
      top: 45%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 1.5rem;
      font-weight: 600;
      color: #2d3748;
    }

    .amount {
      position: absolute;
      top: 60%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 0.75rem;
      font-weight: 500;
      color: #4a5568;
      white-space: nowrap;
    }

    .label {
      text-align: center;
      margin-top: 8px;
      font-size: 0.875rem;
      color: #718096;
    }

    /* Dark mode support */
    :host-context([data-theme="dark"]) .percentage {
      color: #e2e8f0;
    }

    :host-context([data-theme="dark"]) .amount {
      color: #a0aec0;
    }

    :host-context([data-theme="dark"]) .label {
      color: #a0aec0;
    }

    :host-context([data-theme="dark"]) .circle-bg {
      stroke: #4a5568;
    }
  `]
})
export class ProgressCircleComponent {
  @Input() percentage: number = 0;
  @Input() label: string = '';
  @Input() type: 'savings' | 'budget' = 'savings';
  @Input() showAmount: boolean = false;
  @Input() currentAmount?: number;
  @Input() totalAmount?: number;
  @Input() currency: string = 'TND';

  formatAmount(amount: number): string {
    if (amount % 1 === 0) {
      // If the number is a whole number, don't show decimals
      return amount.toString();
    } else {
      // If the number has decimals, show up to 2 decimal places
      return amount.toFixed(2);
    }
  }
}