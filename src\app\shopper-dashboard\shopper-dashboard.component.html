<app-top-navbar></app-top-navbar>
<app-sidebar></app-sidebar>

<div class="dashboard-container">
  <!-- Loading State -->
  <div *ngIf="isLoading()" class="loading-container">
    <div class="loading-spinner"></div>
    <p>Loading dashboard...</p>
  </div>

  <!-- Authentication Warning -->
  <div *ngIf="!isAuthenticated()" class="auth-warning">
    <div class="warning-card">
      <i class="fas fa-exclamation-triangle"></i>
      <h3>Authentication Required</h3>
      <p>Please log in to view your personalized dashboard.</p>
      <button routerLink="/login" class="login-btn">Go to Login</button>
    </div>
  </div>

  <!-- Main Dashboard Content -->
  <div *ngIf="isAuthenticated() && !isLoading()" class="main-content">
    <!-- Lifetime Expenses Card -->
    <div class="card lifetime-expenses">
      <div class="card-header">
        <h2>Life Time Expenses</h2>
      </div>
      <div class="amount">{{ getLifetimeExpensesTotal() }} TND</div>
      <div class="meta-info">
        <div class="articles">{{ getLifetimeExpensesCount() }} Transactions</div>
        <div class="time" *ngIf="cardLifetimeExpenses()">
          Since {{ cardLifetimeExpenses()!.accountCreationDate | date:'mediumDate' }}
        </div>
      </div>
    </div>

    <!-- Current Month Expenses Card -->
    <div class="card current-month">
      <div class="card-header">
        <h2>Expenses Current Month</h2>
      </div>
      <div class="amount">{{ getCurrentMonthTotal() }} TND</div>
      <div class="meta-info">
        <div class="articles">{{ getCurrentMonthCount() }} Transactions</div>
        <div class="time" *ngIf="cardCurrentMonth()">
          {{ getMonthName(cardCurrentMonth()!.month) }} {{ cardCurrentMonth()!.year }}
        </div>
      </div>
    </div>

    <!-- Expenses Category Card -->
    <div class="card expenses-category">
      <div class="card-header">
        <div class="title-wrapper">
          <h2>Expenses</h2>
          <div class="subtitle">Per Category</div>
        </div>
        <div class="dropdown-selector">1 month</div>
      </div>
      <div class="chart-container">
        <canvas id="expensesCategoryChart"></canvas>
      </div>
      <div class="chart-legend">
        <div class="legend-item" *ngFor="let expense of categoryExpenses$ | async">
          <div class="color-indicator" [style.background-color]="expense.color"></div>
          <span>{{ expense.category | titlecase }} ({{ formatAmount(expense.amount) }} TND)</span>
        </div>
      </div>
    </div>

    <!-- Expenses Per Month Card -->
    <div class="card expenses-month">
      <div class="card-header">
        <h2>Expenses Per Month</h2>

      </div>
      <div class="amount">{{ getAverageMonthlyExpenses() }} TND</div>
      <div class="chart-container" [class.scrollable-chart]="(cardExpensesMonth()?.monthlyBreakdown?.length || 0) > 7">
        <canvas id="expensesMonthChart"></canvas>
      </div>
      <div class="monthly-summary" *ngIf="cardExpensesMonth()">
        <div class="summary-item">
          <span>Total: {{ formatAmount(cardExpensesMonth()!.totalAmount) }} TND</span>
        </div>
        <div class="summary-item">
          <span>{{ cardExpensesMonth()!.monthlyBreakdown.length }} months of data</span>
        </div>
      </div>

      <!-- Test Data Controls for Chart Width Testing -->
      <div class="test-controls">
        <div style="margin-bottom: 5px; font-weight: 600; color: #6B48FF; font-size: 12px;">Chart Width Test:</div>
        <button (click)="addFakeMonthlyData()">
          Add 12 Months
        </button>
        <button (click)="clearMonthlyData()">
          Clear Data
        </button>
      </div>
    </div>

    <!-- Last Transactions Card -->
    <div class="card transactions">
      <div class="card-header">
        <h2>Last Transactions</h2>
        <button class="view-all" (click)="openTransactionsPopup()" [disabled]="allTransactions.length === 0">
          View All ({{ allTransactions.length }})
        </button>
      </div>
      <div class="transaction-list">
        <div class="transaction-item" *ngFor="let transaction of transactions">
          <div class="transaction-left">
            <div class="indicator"></div>
            <div class="transaction-date">{{ transaction.date }}, {{ transaction.time }}</div>
          </div>
          <div class="amount">{{ transaction.amount }} TND</div>
        </div>
      </div>
    </div>

    <!-- Saving Plan Card -->
    <div class="card saving-plan">
      <div class="card-header">
        <h2>Saving Plan</h2>
        <div class="menu-dots">⋮</div>
      </div>
      <div class="chart-container">
        <canvas id="savingPlanChart"></canvas>
        <div class="chart-center">
          <div class="saving-label">Total Saving</div>
          <div class="saving-sublabel">{{ getTotalSavingsPercentage() }}%</div>
        </div>
      </div>
      <div class="chart-legend">
        <div class="legend-item" *ngFor="let plan of getSavingPlanItems()">
          <div class="color-indicator" [style.background-color]="plan.color"></div>
          <div class="legend-content">
            <span class="plan-name">{{ plan.name }}</span>
            <span class="plan-details">{{ formatAmount(plan.current) }}/{{ formatAmount(plan.target) }} TND</span>
          </div>
        </div>
        <!-- Fallback if no saving plans -->
        <div *ngIf="getSavingPlanItems().length === 0" class="no-plans">
          <span>No saving plans available</span>
        </div>
      </div>
      <div class="saving-summary" *ngIf="cardSavingPlan()">
        <div class="summary-row">
          <span>Current: {{ getTotalSavingsCurrent() }} TND</span>
          <span>Target: {{ getTotalSavingsTarget() }} TND</span>
        </div>
      </div>

      <!-- Test Data Controls -->
      <div class="test-controls" style="margin-top: 10px; padding: 8px; background: rgba(0,0,0,0.05); border-radius: 8px; font-size: 12px;">
        <div style="margin-bottom: 5px; font-weight: 600; color: #666;">Test Controls:</div>
        <button (click)="addFakeSavingPlans()" style="margin-right: 8px; padding: 6px 12px; font-size: 12px; background: #28a745; color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: 500;">
          Add Fake Data
        </button>
        <button (click)="clearAllSavingPlans()" style="padding: 6px 12px; font-size: 12px; background: #dc3545; color: white; border: none; border-radius: 6px; cursor: pointer; font-weight: 500;">
          Clear All
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Transactions Popup Modal -->
<div class="transactions-popup-overlay" *ngIf="showTransactionsPopup" (click)="onPopupBackdropClick($event)">
  <div class="transactions-popup">
    <div class="popup-header">
      <h2>All Transactions</h2>
      <button class="close-btn" (click)="closeTransactionsPopup()">
        <i class="fas fa-times"></i>
      </button>
    </div>

    <div class="popup-content">
      <div class="transactions-summary">
        <div class="summary-item">
          <span class="label">Total Transactions:</span>
          <span class="value">{{ allTransactions.length }}</span>
        </div>
        <div class="summary-item">
          <span class="label">Total Amount:</span>
          <span class="value">{{ calculateTotalAmount() }} TND</span>
        </div>
      </div>

      <div class="transactions-table-container">
        <table class="transactions-table">
          <thead>
            <tr>
              <th>Ticket #</th>
              <th>Product</th>
              <th>Date</th>
              <th>Amount</th>
              <th>Payment</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let transaction of allTransactions; let i = index" class="transaction-row">
              <td class="ticket-number">#{{ transaction.ticketNumber }}</td>
              <td class="product-info">
                <div class="product-details">
                  <img [src]="transaction.productImage || 'assets/images/default-product.jpg'"
                       alt="Product" class="product-image">
                  <div class="product-text">
                    <span class="product-name">{{ transaction.productName || 'Product' }}</span>
                    <span class="brand-name">{{ transaction.brandName || 'Brand' }}</span>
                  </div>
                </div>
              </td>
              <td class="date">{{ transaction.date }}</td>
              <td class="amount">
                <span class="amount-value">{{ transaction.amount }}</span>
              </td>
              <td class="payment-mode">{{ transaction.paymentMode || 'Cash' }}</td>
              <td class="status">
                <span class="status-badge" [ngClass]="getStatusClass(transaction.status)">
                  {{ transaction.status || 'Completed' }}
                </span>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Empty state -->
        <div class="empty-state" *ngIf="allTransactions.length === 0">
          <i class="fas fa-receipt"></i>
          <h3>No Transactions Found</h3>
          <p>You haven't made any transactions yet.</p>
        </div>
      </div>
    </div>
  </div>
</div>