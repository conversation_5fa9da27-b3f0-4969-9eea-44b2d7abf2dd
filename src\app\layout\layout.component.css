.main-content {
  margin-left: 250px;
  padding: 20px;
  height: 100%; /* Use height instead of min-height */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  position: relative;
  z-index: 1;

  /* Specific fix for shopper-dashboard component */
  app-shopper-dashboard {
    margin-left: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    position: relative;
    z-index: 2;
  }
}

.mobile-user-info {
  display: none;
}

/* Desktop-specific layout fixes */
@media (min-width: 769px) {
  .main-content {
    margin-left: 250px;
    padding: 20px;
    width: calc(100% - 250px);
    max-width: calc(100vw - 250px);
    position: relative;
    z-index: 1;

    app-shopper-dashboard {
      margin-left: 0 !important;
      width: 100% !important;
      max-width: 100% !important;
      position: relative;
      z-index: 2;
    }
  }
}

@media (max-width: 768px) {
  .main-content {
    margin-left: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    max-width: 100vw !important;
  }

  // Specific fix for shopper-dashboard
  .main-content app-shopper-dashboard {
    margin-left: 0 !important;
    width: 100% !important;
    max-width: 100vw !important;
  }

  .mobile-user-info {
    display: block;
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 1001;
  }

  .mobile-user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
  }
}