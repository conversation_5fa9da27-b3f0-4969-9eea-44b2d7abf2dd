import { Component, OnInit, OnDestroy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule, ActivatedRoute } from '@angular/router';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { BudgetData, BudgetPeriod } from '../interfaces/budget';
import { BudgetDataService } from '../core/budget/budget-data.service';
import { ThemeService } from '../services/theme.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-edit-budget',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    TopNavbarComponent,
    SidebarComponent
  ],
  templateUrl: './edit-budget.component.html',
  styleUrls: ['./edit-budget.component.scss']
})
export class EditBudgetComponent implements OnInit, OnDestroy {
  private budgetDataService = inject(BudgetDataService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private themeService = inject(ThemeService);

  // Expose BudgetPeriod enum to the template
  BudgetPeriod = BudgetPeriod;

  budget: BudgetData = {
    id: '',
    name: '',
    period: BudgetPeriod.OneMonth,
    amount: 0,
    category: '',
    createdAt: new Date(),
    notifications: {
      budgetOverrun: true,
      riskOfOverrun: true
    },
    userId: '',
    spent: 0,
    remaining: 0,
    percentSpent: 0
  };

  originalBudget: BudgetData | null = null;
  availableCategories: string[] = [];
  showNotificationSettings: boolean = false;
  isDarkMode: boolean = false;
  isLoading: boolean = true;
  notFound: boolean = false;
  private destroy$ = new Subject<void>();

  ngOnInit(): void {
    // Subscribe to theme changes
    this.themeService.isDarkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isDark => {
        this.isDarkMode = isDark;
      });

    this.availableCategories = this.budgetDataService.getAvailableCategories();

    // Get budget ID from route params
    this.route.paramMap.subscribe(params => {
      const budgetId = params.get('id');
      if (budgetId) {
        this.loadBudget(budgetId);
      } else {
        this.notFound = true;
        this.isLoading = false;
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadBudget(budgetId: string): void {
    const budget = this.budgetDataService.getBudgetById(budgetId);
    if (budget) {
      // Make a deep copy of the budget to avoid modifying the original directly
      this.originalBudget = JSON.parse(JSON.stringify(budget));
      this.budget = JSON.parse(JSON.stringify(budget));
      this.isLoading = false;
    } else {
      this.notFound = true;
      this.isLoading = false;
    }
  }

  toggleNotificationSettings(): void {
    this.showNotificationSettings = !this.showNotificationSettings;
  }

  updateBudget(): void {
    if (this.validateBudget()) {
      // Update the budget using the signal service
      this.budgetDataService.updateBudget(this.budget).subscribe({
        next: (updatedBudget) => {
          if (updatedBudget) {
            this.router.navigate(['/budget']);
          }
        },
        error: (error) => {
          console.error('Error updating budget:', error);
          alert('Failed to update budget. Please try again.');
        }
      });
    }
  }

  cancel(): void {
    this.router.navigate(['/budget']);
  }

  deleteBudget(): void {
    if (confirm('Are you sure you want to delete this budget?')) {
      this.budgetDataService.deleteBudget(this.budget.id).subscribe({
        next: (success) => {
          if (success) {
            this.router.navigate(['/budget']);
          }
        },
        error: (error) => {
          console.error('Error deleting budget:', error);
          alert('Failed to delete budget. Please try again.');
        }
      });
    }
  }

  private validateBudget(): boolean {
    // Basic validation
    if (!this.budget.name || this.budget.name.trim() === '') {
      alert('Please enter a budget name');
      return false;
    }

    if (!this.budget.amount || this.budget.amount <= 0) {
      alert('Please enter a valid budget amount');
      return false;
    }

    if (!this.budget.category || this.budget.category.trim() === '') {
      alert('Please select a category');
      return false;
    }

    return true;
  }
}
