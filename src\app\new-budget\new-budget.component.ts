import { Component, OnInit, OnDestroy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule, ActivatedRoute } from '@angular/router';
import { TopNavbarComponent } from '../top-navbar/top-navbar.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { BudgetData, BudgetPeriod } from '../interfaces/budget';
import { BudgetDataService } from '../core/budget/budget-data.service';
import { ThemeService } from '../services/theme.service';
import { NotificationService } from '../services/notification.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-new-budget',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    TopNavbarComponent,
    SidebarComponent
  ],
  templateUrl: './new-budget.component.html',
  styleUrls: ['./new-budget.component.scss']
})
export class NewBudgetComponent implements OnInit, OnDestroy {
  private budgetDataService = inject(BudgetDataService);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private themeService = inject(ThemeService);
  private notificationService = inject(NotificationService);

  // Expose BudgetPeriod enum to the template
  BudgetPeriod = BudgetPeriod;

  budget: Omit<BudgetData, 'id' | 'createdAt' | 'userId' | 'spent' | 'remaining' | 'percentSpent'> = {
    name: '',
    period: BudgetPeriod.OneMonth,
    amount: 0,
    category: '',
    notifications: {
      budgetOverrun: true,
      riskOfOverrun: true
    }
  };

  availableCategories: string[] = [];
  showNotificationSettings: boolean = false;
  isDarkMode: boolean = false;
  pendingAmount: number = 0;
  autoAddToNewBudget: boolean = false;
  private destroy$ = new Subject<void>();

  ngOnInit(): void {
    // Subscribe to theme changes
    this.themeService.isDarkMode$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isDark => {
        this.isDarkMode = isDark;
      });

    // Get available categories
    this.availableCategories = this.budgetDataService.getAvailableCategories();

    // Check for pending amount from query parameters
    this.route.queryParams
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        if (params['pendingAmount']) {
          this.pendingAmount = parseFloat(params['pendingAmount']);
          this.autoAddToNewBudget = params['autoAdd'] === 'true';

          // Show a message about the pending amount
          if (this.pendingAmount > 0) {
            // Set a minimum budget amount to accommodate the pending amount
            this.budget.amount = Math.max(this.pendingAmount * 2, 100);
          }
        } else {
          // Check localStorage as a fallback
          const storedAmount = localStorage.getItem('pending-budget-amount');
          if (storedAmount) {
            this.pendingAmount = parseFloat(storedAmount);
            this.autoAddToNewBudget = true;

            // Set a minimum budget amount to accommodate the pending amount
            this.budget.amount = Math.max(this.pendingAmount * 2, 100);

            // Clear the stored amount to prevent it from being used again
            localStorage.removeItem('pending-budget-amount');
          }
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleNotificationSettings(): void {
    this.showNotificationSettings = !this.showNotificationSettings;
  }

  saveBudget(): void {
    if (this.validateBudget()) {
      // Add the budget using the signal service
      this.budgetDataService.addBudget(this.budget).subscribe({
        next: (newBudget) => {
          if (newBudget) {
            // If there's a pending amount and autoAddToNewBudget is true, add it to the budget
            if (this.pendingAmount > 0 && this.autoAddToNewBudget) {
              // Add the pending amount to the budget
              this.budgetDataService.addTicketsToBudget(newBudget.id, this.pendingAmount).subscribe({
                next: (updatedBudget) => {
                  if (updatedBudget) {
                    // Show a notification
                    this.notificationService.addNotification({
                      title: 'Budget Updated',
                      message: `Added ${this.pendingAmount} TND to ${updatedBudget.name}`,
                      read: false,
                      time: new Date()
                    });
                  }
                },
                error: (error) => {
                  console.error('Error adding tickets to budget:', error);
                }
              });
            }

            // Navigate back to the budget page
            this.router.navigate(['/budget']);
          }
        },
        error: (error) => {
          console.error('Error creating budget:', error);
          alert('Failed to create budget. Please try again.');
        }
      });
    }
  }

  cancel(): void {
    this.router.navigate(['/budget']);
  }

  private validateBudget(): boolean {
    // Basic validation
    if (!this.budget.name || this.budget.name.trim() === '') {
      alert('Please enter a budget name');
      return false;
    }

    if (!this.budget.amount || this.budget.amount <= 0) {
      alert('Please enter a valid budget amount');
      return false;
    }

    if (!this.budget.category || this.budget.category.trim() === '') {
      alert('Please select a category');
      return false;
    }

    return true;
  }
}
