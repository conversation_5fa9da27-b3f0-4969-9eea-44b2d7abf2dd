<app-top-navbar></app-top-navbar>
<app-sidebar></app-sidebar>

<div class="receipt-container">
  <div class="receipt-layout">
    <div class="receipt-card">
      <div class="receipt-card-actions no-export">
        <button class="refund-btn" (click)="refundArticle()" *ngIf="isCollaboratingBrand(receipt.merchantName)">Refund An Article</button>
        <button class="export-btn" (click)="exportToPDF()">Export Receipt</button>
      </div>
      <div class="receipt-header">        <div class="merchant-info">
          <div class="merchant-logo-container">
            <ng-container *ngIf="isCollaboratingBrand(receipt.merchantName); else unknownBrand">
              <img [src]="receipt.merchantLogo" [alt]="receipt.merchantName" class="merchant-logo">
            </ng-container>
            <ng-template #unknownBrand>
              <div class="merchant-logo unknown-brand">
                <i class="fas fa-store text-gray-400 text-4xl"></i>
              </div>
            </ng-template>
          </div>
          <h2>{{receipt.merchantName}}</h2>
          <p class="address" *ngIf="isCollaboratingBrand(receipt.merchantName)">Rue Yacer Arafat,Sahloul,Sousse, TN, 4021</p>
          <p class="phone" *ngIf="isCollaboratingBrand(receipt.merchantName)">Phone Number: +216 52 533 533</p>
        </div>
        <div class="receipt-info" *ngIf="isCollaboratingBrand(receipt.merchantName)">
          <div class="rating">
            <div class="stars" *ngIf="receipt.rating">
              <i class="fas fa-star"></i>
              <i class="fas fa-star" *ngIf="receipt.rating >= 2"></i>
              <i class="fas fa-star" *ngIf="receipt.rating >= 3"></i>
              <i class="fas fa-star" *ngIf="receipt.rating >= 4"></i>
              <i class="fas fa-star-half-alt" *ngIf="receipt.rating % 1 >= 0.5"></i>
              <i class="far fa-star" *ngIf="receipt.rating % 1 < 0.5 && receipt.rating < 5"></i>
            </div>
            <span class="rating-value" *ngIf="receipt.rating">{{receipt.rating}}/5</span>
          </div>
        </div>
      </div>

      <div class="receipt-details">
        <div class="receipt-meta">
          <p>Receipt No.: {{receipt.receiptNumber}}</p>
          <p>Product ID: {{receipt.productId}}</p>
          <p>Date: {{receipt.date}}</p>
          <p *ngIf="receipt.status">Status: <span [class]="'status-' + receipt.status.toLowerCase()">{{receipt.status}}</span></p>
        </div>

        <div class="receipt-items">
          <div class="product-list">
            <!-- Display products for registered brands -->
            <div *ngIf="receipt.products && receipt.products.length > 0" class="product-rows">
              <div *ngFor="let product of receipt.products; let i = index" class="product-row">
                <div class="product-info">
                  <span class="product-name">{{product.name}}</span>
                  <div class="price-details">
                    <span class="quantity">x1</span>
                    <span class="price">{{product.price}}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Fallback for non-registered brands or missing product data -->
            <div *ngIf="!receipt.products || receipt.products.length === 0" class="product-row">
              <div class="product-info">
                <span class="product-name">General Purchase</span>
                <div class="price-details">
                  <span class="quantity">x1</span>
                  <span class="price">{{receipt.totalAmount}} TND</span>
                </div>
              </div>
            </div>

            <div class="totals-section">
              <div class="subtotal-row">
                <span>Subtotal:</span>
                <span>{{receipt.totalAmount - (receipt.totalAmount * 0.19) | number:'1.2-2'}} TND</span>
              </div>
              <div class="vat-row">
                <span>VAT (19%):</span>
                <span>{{receipt.totalAmount * 0.19 | number:'1.2-2'}} TND</span>
              </div>
              <div class="total-row">
                <span>Total:</span>
                <span>{{receipt.totalAmount | number:'1.2-2'}} TND</span>
              </div>
            </div>
          </div>
        </div>

        <div class="receipt-summary">
          <div class="summary-row">
            <span>Sum incl. VAT</span>
            <span>{{receipt.totalAmount | number:'1.2-2'}} TND</span>
          </div>
          <div class="summary-row discount" *ngIf="receipt.discounts">
            <span>Discount total incl. VAT</span>
            <span>{{receipt.discounts | number:'1.2-2'}} TND</span>
          </div>
          <div class="summary-row total">
            <span>Sum total incl. VAT</span>
            <span>{{receipt.totalAmount | number:'1.2-2'}} TND</span>
          </div>
        </div>

        <div class="payment-info">
          <div class="payment-method">
            <span>Payment Method</span>
            <div class="payment-method-container">
              <span class="payment-label">{{receipt.paymentMethod}}</span>
              <!-- Credit Card Icons -->
              <span class="payment-icon" *ngIf="receipt.paymentMethod === 'Credit Card'"><i class="fas fa-credit-card"></i></span>
              <span class="payment-icon" *ngIf="receipt.paymentMethod === 'Visa'"><i class="fab fa-cc-visa"></i></span>
              <span class="payment-icon" *ngIf="receipt.paymentMethod === 'MasterCard'"><i class="fab fa-cc-mastercard"></i></span>
              <span class="payment-icon" *ngIf="receipt.paymentMethod === 'American Express'"><i class="fab fa-cc-amex"></i></span>
              <span class="payment-icon" *ngIf="receipt.paymentMethod === 'Discover'"><i class="fab fa-cc-discover"></i></span>

              <!-- Cash and Bank Transfer Icons -->
              <span class="payment-icon" *ngIf="receipt.paymentMethod === 'Cash'"><i class="fas fa-money-bill-wave"></i></span>
              <span class="payment-icon" *ngIf="receipt.paymentMethod === 'Transfer Bank' || receipt.paymentMethod === 'Bank Transfer'"><i class="fas fa-university"></i></span>

              <!-- Digital Payment Icons -->
              <span class="payment-icon" *ngIf="receipt.paymentMethod === 'PayPal'"><i class="fab fa-paypal"></i></span>
              <span class="payment-icon" *ngIf="receipt.paymentMethod === 'Apple Pay'"><i class="fab fa-apple-pay"></i></span>
              <span class="payment-icon" *ngIf="receipt.paymentMethod === 'Google Pay'"><i class="fab fa-google-pay"></i></span>

              <!-- Delivery Payment Icons -->
              <span class="payment-icon" *ngIf="receipt.paymentMethod === 'Cash on Delivery' || receipt.paymentMethod.includes('Delivery')"><i class="fas fa-truck"></i></span>

              <!-- Default Icon for other payment methods -->
              <span class="payment-icon" *ngIf="!['Credit Card', 'Visa', 'MasterCard', 'American Express', 'Discover', 'Cash', 'Transfer Bank', 'Bank Transfer', 'PayPal', 'Apple Pay', 'Google Pay', 'Cash on Delivery'].includes(receipt.paymentMethod)"><i class="fas fa-money-check-alt"></i></span>
            </div>
          </div>
          <div class="articles-count">
            <span>Number Of Articles</span>
            <span>{{receipt.products && receipt.products.length > 0 ? receipt.products.length : receipt.items.length}}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- New Sidebar Section -->
    <div class="receipt-sidebar" *ngIf="isCollaboratingBrand(receipt.merchantName)">
      <!-- Brand products section -->
      <div class="sidebar-section recommendations">
        <h3 class="sidebar-title">{{currentBrand | titlecase}} Products</h3>
        <div class="product-recommendations">
          <swiper-container [config]="swiperConfig">
            <swiper-slide *ngFor="let product of recommendedProducts">
              <div class="recommendation-item" (click)="viewRecommendedProduct(product)">
                <img [src]="product.image" [alt]="product.name" class="product-image">
                <div class="recommendation-details">
                  <p class="product-title">{{product.name}}</p>
                  <p class="brand-name">{{product.brand | titlecase}}</p>
                </div>
              </div>
            </swiper-slide>
          </swiper-container>
        </div>
      </div>

      <!-- Rating section -->
      <div class="sidebar-section rating-section" *ngIf="!ratingSubmitted">
        <h3 class="rating-title">Rate <span class="brand-highlight">{{receipt.merchantName}}</span></h3>
        <p class="rating-subtitle">How was your shopping experience?</p>

        <div class="rating-stars">
          <i [class]="userRating >= 1 ? 'fas fa-star' : 'far fa-star'" (click)="setRating(1)"></i>
          <i [class]="userRating >= 2 ? 'fas fa-star' : 'far fa-star'" (click)="setRating(2)"></i>
          <i [class]="userRating >= 3 ? 'fas fa-star' : 'far fa-star'" (click)="setRating(3)"></i>
          <i [class]="userRating >= 4 ? 'fas fa-star' : 'far fa-star'" (click)="setRating(4)"></i>
          <i [class]="userRating >= 5 ? 'fas fa-star' : 'far fa-star'" (click)="setRating(5)"></i>
        </div>

        <div class="rating-labels">
          <span>Bad</span>
          <span>Good</span>
        </div>

        <button class="confirm-button" (click)="submitRating()">Confirm</button>
      </div>

      <!-- Thank you message after rating -->
      <div class="sidebar-section rating-section" *ngIf="ratingSubmitted">
        <h3 class="rating-title">Thank you for your feedback!</h3>
        <p class="rating-subtitle">Your rating for <span class="brand-highlight">{{receipt.merchantName}}</span> has been saved.</p>
        <div class="rating-stars">
          <i class="fas fa-star" *ngFor="let star of [].constructor(userRating)"></i>
          <i class="far fa-star" *ngFor="let star of [].constructor(5 - userRating)"></i>
        </div>
        <p class="rating-value">{{userRating}}/5</p>
      </div>

      <!-- Newsletter section -->
      <div class="sidebar-section newsletter-section" *ngIf="!subscriptionHidden">
        <!-- Subscription form (shown when not in success state) -->
        <div *ngIf="!subscriptionSubmitted">
          <h3 class="newsletter-title">Subscribe to <span class="brand-highlight">{{receipt.merchantName}}</span> newsletter and stay up to date!</h3>

          <div class="newsletter-form">
            <input type="email" placeholder="Email*" class="email-input" [ngClass]="{'previously-used': isPreviouslyUsedEmail}" [(ngModel)]="emailAddress">
            <button class="subscribe-button" (click)="subscribeToNewsletter()">Subscribe</button>
          </div>

          <div class="social-icons">
            <a href="#" class="social-icon facebook"><i class="fab fa-facebook-f"></i></a>
            <a href="#" class="social-icon instagram"><i class="fab fa-instagram"></i></a>
            <a href="#" class="social-icon linkedin"><i class="fab fa-linkedin-in"></i></a>
            <a href="#" class="social-icon youtube"><i class="fab fa-youtube"></i></a>
            <a href="#" class="social-icon whatsapp"><i class="fab fa-whatsapp"></i></a>
            <a href="#" class="social-icon messenger"><i class="fab fa-facebook-messenger"></i></a>
          </div>
        </div>

        <!-- Success message (shown briefly after successful subscription) -->
        <div class="success-message-container" *ngIf="subscriptionSubmitted">
          <div class="success-icon">
            <i class="fas fa-check-circle"></i>
          </div>
          <h3 class="success-title">Successfully Subscribed!</h3>
          <p class="success-text">Thank you for subscribing to our newsletter.</p>
        </div>
      </div>

      <!-- Eco-friendly footer -->
      <div class="sidebar-section eco-footer">
        <div class="eco-icons">
          <div class="eco-icon tree">
            <i class="fas fa-tree"></i>
          </div>
          <div class="eco-icon leaf">
            <i class="fas fa-leaf"></i>
          </div>
          <div class="eco-icon seedling">
            <i class="fas fa-seedling"></i>
          </div>
        </div>
        <p class="eco-message">With this receipt you are saving 155.6 cm² of paper and 0.8g of CO₂.</p>
        <div class="eco-details">
          <div class="eco-detail">
            <i class="fas fa-recycle"></i>
            <span>Eco-friendly</span>
          </div>
          <div class="eco-detail">
            <i class="fas fa-cloud"></i>
            <span>Reduced emissions</span>
          </div>
        </div>
        <p class="company-slogan">Smart Retail, Bright Future - Receeto.com</p>
      </div>
    </div>
  </div>
</div>
